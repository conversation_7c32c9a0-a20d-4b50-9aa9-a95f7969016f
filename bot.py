import discord
import re
import logging
import datetime
import json
import base64
import os
import sys
import urllib.request
import aiohttp
import asyncio
from discord.ext import commands
from openai import OpenAI
import yfinance as yf
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("unified_bot.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UnifiedBot')

# Load environment variables
load_dotenv()

# Get Discord token from environment variables
DISCORD_TOKEN = os.getenv('DISCORD_TOKEN')
if not DISCORD_TOKEN or len(DISCORD_TOKEN) < 50:
    logger.critical("DISCORD_TOKEN missing or invalid format")
    sys.exit(1)

# Get OpenAI API key from environment variables
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
if not OPENAI_API_KEY or len(OPENAI_API_KEY) < 20:
    logger.critical("OPENAI_API_KEY missing or invalid format")
    sys.exit(1)

# Initialize OpenAI client
openai_client = OpenAI(api_key=OPENAI_API_KEY)

# Bot configuration
BOT_CATEGORY_NAME = os.getenv('BOT_CATEGORY_NAME', "Trading Bot")
PORTFOLIO_CHANNEL_NAME = os.getenv('PORTFOLIO_CHANNEL_NAME', "portfolio-tracker")
DATA_FILE = os.getenv('DATA_FILE', "portfolio_data.json")
GUILD_ID = int(os.getenv('GUILD_ID', 0))

# Rate limiting control
RATE_LIMITED = False
RATE_LIMIT_RESET_TIME = 0
MAX_REQUESTS_PER_HOUR = 10

# Price caching system - simple version
PRICE_CACHE = {}  # {ticker: {"price": float, "timestamp": float}}

# Sentiment tracking
TICKER_SENTIMENT = {}  # {ticker: {"bullish": count, "bearish": count}}

# Status thresholds
BULLISH_THRESHOLD = 2
BEARISH_THRESHOLD = 1

# Initialize bot with intents
intents = discord.Intents.default()
intents.message_content = True
intents.messages = True
bot = commands.Bot(command_prefix='!', intents=intents)

# Global variables
portfolio_channel = None

# Data structure for portfolio and wishlist
# {
#   "user_id": {
#     "trades": [{"ticker": str, "shares": float, "price": float, "type": str, "timestamp": str}],
#     "holdings": {"ticker": shares},
#     "avg_price": {"ticker": avg_price},
#     "watchlist": {
#       "ticker": {
#         "status": str,  # "buy", "neutral_bullish", "neutral_bearish", "sell"
#         "risk_profile": str,  # "A", "B", "C"
#         "entry_price": float,  # Entry/Buy price
#         "target_price": float,  # Target/Sell price
#         "added_at": str,  # timestamp
#         "analysis": str,  # Full analysis text
#         "last_updated": str  # Last update timestamp
#       }
#     }
#   }
# }

# Data management functions
def load_data():
    """Load portfolio data from JSON file"""
    try:
        with open(DATA_FILE, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def save_data(data):
    """Save portfolio data to JSON file"""
    with open(DATA_FILE, 'w') as f:
        json.dump(data, f, indent=2)

# Chart analysis functions
async def analyze_chart_image(image_data, base64_image):
    """Analyze a chart image using OpenAI Vision"""
    try:
        # Check if we have valid image data
        if not base64_image or not image_data:
            logger.error("Invalid image data provided for analysis")
            return None

        # Set up retry mechanism
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                response = openai_client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": "Du bist ein professioneller technischer Analyst mit 15+ Jahren Erfahrung. Analysiere das Chart-Bild systematisch und logisch basierend auf etablierten technischen Indikatoren und Chartmustern.

STRUKTUR:
1. Identifiziere zuerst das Ticker-Symbol und Unternehmen (meist oben links) und beginne mit: 'TICKER: [Symbol] - [Name]'
2. TECHNISCHE ANALYSE (basiere dich NUR auf sichtbare Daten):
   - Trendrichtung (Aufwärts/Abwärts/Seitwärts) basierend auf Preisbewegung
   - Unterstützungs- und Widerstandsniveaus (horizontale/diagonale Linien)
   - Volumen-Analyse (falls sichtbar)
   - Technische Indikatoren (MACD, RSI, Moving Averages falls sichtbar)
   - Chartmuster (Dreiecke, Flaggen, Kopf-Schulter, etc.)
3. PROGNOSE (1-5 Tage):
   - Wahrscheinlichste Preisrichtung mit Begründung
   - Entry Point: Optimaler Einstiegspreis basierend auf Support/Resistance
   - Profit Target: Realistisches Kursziel basierend auf technischen Levels
4. Beende mit: 'Signal: [BUY/SELL/NEUTRAL BUY/NEUTRAL SELL] - [A/B/C]'

WICHTIG: Verwende nur sichtbare technische Daten. Keine Fantasie oder Spekulationen. Sei konservativ bei BUY/SELL Signalen."},
                        {"role": "user", "content": [
                            {"type": "text", "text": "Analysiere dieses Aktien-Chart-Bild systematisch und gib mir eine fundierte technische Einschätzung. WICHTIG: Identifiziere unbedingt das korrekte Ticker-Symbol und den vollständigen Unternehmensnamen aus dem Chart. Achte besonders auf die Beschriftung oben links oder in der Titelleiste des Charts. Basiere deine Analyse ausschließlich auf sichtbaren technischen Daten und Chartmustern."},
                            {"type": "image_url", "image_url": {"url": base64_image}}
                        ]}
                    ],
                    max_tokens=1500,
                    temperature=0.3
                )

                # If we get here, the API call was successful
                break

            except Exception as retry_error:
                retry_count += 1
                logger.warning(f"API call attempt {retry_count} failed: {retry_error}")

                if retry_count >= max_retries:
                    logger.error(f"Failed to analyze chart after {max_retries} attempts")
                    return None

                # Wait before retrying (exponential backoff)
                await asyncio.sleep(2 ** retry_count)

        # Process the successful response
        analysis = response.choices[0].message.content
        logger.info(f"Successfully analyzed chart image, extracted text length: {len(analysis)}")

        # Process the analysis to extract ticker, company name, and status
        result = process_chart_analysis(analysis)

        if result and result["ticker"] != "UNKNOWN":
            logger.info(f"Identified ticker: {result['ticker']} with status: {result['status']}, risk: {result['risk_class']}")

            # Log the formatted analysis that will be sent to the channel
            logger.info(f"Formatted analysis (last 200 chars): {result['formatted_analysis'][-200:]}")

            # Process the formatted analysis (which includes the TICKER-STATUS-RISK format)
            updates = await process_bot_message(result["formatted_analysis"])
            logger.info(f"Updated {updates} watchlists based on the analysis")
        else:
            logger.warning("Could not identify ticker from chart analysis")

        return result
    except Exception as e:
        logger.error(f"Error analyzing chart image: {e}")
        return None

def process_chart_analysis(analysis):
    """Extract ticker, company name, and status from analysis"""
    company_name = "Unbekanntes Unternehmen"
    ticker_symbol = "UNKNOWN"
    stock_status = ""
    risk_class = "C"  # Default risk class
    entry_targets = ""
    analysis_text = analysis

    # Extract ticker and company name
    extracted_ticker = None
    extracted_company = None

    # First, try to extract from the TICKER: format (most reliable)
    if "TICKER:" in analysis:
        try:
            ticker_line = analysis.split("\n")[0] if "\n" in analysis else analysis
            if "TICKER:" in ticker_line:
                # Extract ticker symbol
                ticker_match = re.search(r"TICKER:\s*([A-Z]{1,6})", ticker_line, re.IGNORECASE)
                if ticker_match:
                    extracted_ticker = ticker_match.group(1).upper()
                    logger.info(f"Extracted ticker from TICKER: format: {extracted_ticker}")

                # Extract company name
                company_match = re.search(r"TICKER:\s*[A-Z]{1,6}\s*-\s*(.*)", ticker_line, re.IGNORECASE)
                if company_match:
                    extracted_company = company_match.group(1).strip()
                    logger.info(f"Extracted company name from TICKER: format: {extracted_company}")
        except Exception as e:
            logger.error(f"Error extracting ticker from TICKER: format: {e}")

    # Second, try to extract from the first line if it has a format like "Company Name (TICKER)"
    if (not extracted_ticker or not extracted_company) and "\n" in analysis:
        try:
            first_line = analysis.split("\n")[0].strip()
            # Look for patterns like "Company Name (TICKER)" or "Company Name - TICKER"
            company_ticker_match = re.search(r"^(.*?)\s*(?:\(([A-Z]{1,6})\)|[-–—]\s*([A-Z]{1,6}))$", first_line)
            if company_ticker_match:
                potential_company = company_ticker_match.group(1).strip()
                potential_ticker = company_ticker_match.group(2) or company_ticker_match.group(3)

                if potential_ticker and not extracted_ticker:
                    extracted_ticker = potential_ticker.upper()
                    logger.info(f"Extracted ticker from first line: {extracted_ticker}")

                if potential_company and not extracted_company:
                    extracted_company = potential_company
                    logger.info(f"Extracted company name from first line: {extracted_company}")
        except Exception as e:
            logger.error(f"Error extracting from first line: {e}")

    # Third, look for ticker symbols in the text
    if not extracted_ticker:
        try:
            # Look for ticker symbols (1-6 uppercase letters)
            ticker_pattern = r'\b([A-Z]{1,6})\b'
            ticker_matches = re.findall(ticker_pattern, analysis)

            # Filter out common words and abbreviations
            common_words = {"A", "I", "AM", "PM", "THE", "AND", "OR", "IF", "IN", "ON", "AT", "TO", "BY", "FOR",
                           "CEO", "CFO", "CTO", "COO", "USD", "EUR", "GBP", "JPY", "RSI", "MACD", "EMA", "SMA", "ATR"}
            filtered_tickers = [t for t in ticker_matches if t not in common_words]

            # Count occurrences of each potential ticker
            ticker_counts = {}
            for t in filtered_tickers:
                ticker_counts[t] = ticker_counts.get(t, 0) + 1

            # Sort by frequency (most frequent first)
            sorted_tickers = sorted(ticker_counts.items(), key=lambda x: x[1], reverse=True)

            if sorted_tickers:
                extracted_ticker = sorted_tickers[0][0]
                logger.info(f"Extracted most frequent ticker from text: {extracted_ticker} (appeared {sorted_tickers[0][1]} times)")
        except Exception as e:
            logger.error(f"Error extracting ticker from text: {e}")

    # Use the extracted values if available
    if extracted_ticker:
        ticker_symbol = extracted_ticker

    if extracted_company:
        company_name = extracted_company

    # If we have both ticker and company, log the match and verify consistency
    if ticker_symbol != "UNKNOWN" and company_name != "Unbekanntes Unternehmen":
        logger.info(f"Identified stock: {company_name} ({ticker_symbol})")

        # Verify that the ticker symbol appears in the company name or vice versa
        # This helps catch mismatches where the wrong ticker was extracted
        ticker_in_company = ticker_symbol.lower() in company_name.lower()
        company_words = set(re.findall(r'\b\w+\b', company_name.lower()))
        ticker_matches_word = any(word.startswith(ticker_symbol.lower()) for word in company_words)

        if not ticker_in_company and not ticker_matches_word:
            logger.warning(f"Possible mismatch between ticker {ticker_symbol} and company name {company_name}")

            # If we have a clear mismatch, try to extract a better ticker from the company name
            if len(company_words) > 0:
                # Look for capitalized words in the company name that might be the ticker
                cap_words = re.findall(r'\b([A-Z]{1,6})\b', company_name)
                if cap_words:
                    potential_ticker = cap_words[0]
                    logger.info(f"Found potential ticker {potential_ticker} in company name {company_name}")
                    if potential_ticker != ticker_symbol:
                        logger.info(f"Replacing ticker {ticker_symbol} with {potential_ticker} from company name")
                        ticker_symbol = potential_ticker
    elif ticker_symbol != "UNKNOWN":
        logger.info(f"Identified ticker: {ticker_symbol} (company name unknown)")
    elif company_name != "Unbekanntes Unternehmen":
        logger.info(f"Identified company: {company_name} (ticker unknown)")

        # Try to extract ticker from company name if we have company but no ticker
        cap_words = re.findall(r'\b([A-Z]{1,6})\b', company_name)
        if cap_words:
            potential_ticker = cap_words[0]
            logger.info(f"Extracted potential ticker {potential_ticker} from company name {company_name}")
            ticker_symbol = potential_ticker

    # Search for the "Signal: STATUS - RISK" format that GPT uses
    signal_pattern = r'Signal:\s*(BUY|SELL|NEUTRAL BUY|NEUTRAL SELL)\s*-\s*([ABC])'
    signal_match = re.search(signal_pattern, analysis, re.IGNORECASE)

    if signal_match:
        stock_status = signal_match.group(1).upper()
        risk_class = signal_match.group(2).upper()
        logger.info(f"Found signal format: {stock_status} - {risk_class}")
    else:
        # Search for combined status and risk class in other formats
        status_patterns = [
            r'\b(BUY|SELL|NEUTRAL BUY|NEUTRAL SELL)\s*([ABC])\b',
        ]

        for pattern in status_patterns:
            status_match = re.search(pattern, analysis, re.IGNORECASE)
            if status_match:
                stock_status = status_match.group(1).upper()
                risk_class = status_match.group(2).upper()
                break

        # If no combined status found, search for status alone
        if not stock_status:
            basic_status_patterns = [
                r'\b(BUY)\b', r'\b(SELL)\b', r'\b(NEUTRAL BUY)\b', r'\b(NEUTRAL SELL)\b'
            ]

            for pattern in basic_status_patterns:
                status_match = re.search(pattern, analysis, re.IGNORECASE)
                if status_match:
                    stock_status = status_match.group(1).upper()
                    break

    # Map status to internal format
    status_mapping = {
        "BUY": "buy",
        "NEUTRAL BUY": "neutral_bullish",
        "NEUTRAL BULLISH": "neutral_bullish",
        "NEUTRAL SELL": "neutral_bearish",
        "NEUTRAL BEARISH": "neutral_bearish",
        "SELL": "sell"
    }

    internal_status = status_mapping.get(stock_status, "neutral_bullish")
    logger.info(f"Mapped status '{stock_status}' to internal status '{internal_status}'")

    # Extract entry and target prices with improved patterns
    # Handle various formats including those with parentheses and additional text
    entry_pattern = r'Entry\s*(?:Point|Price)?:\s*\$?(\d+\.?\d*)(?:\s*\([^)]*\))?'
    target_pattern = r'(?:Target|Profit)(?:\s*Target)?:\s*\$?(\d+\.?\d*)'

    # Also look for Buy/Sell format in alert zones
    buy_pattern = r'Buy:\s*\$?(\d+\.?\d*)'
    sell_pattern = r'Sell:\s*\$?(\d+\.?\d*)'

    # Look for German format as well (with more variations)
    german_entry_pattern = r'Entry\s*(?:Point|Price)?:\s*(\d+[,.]\d*)\s*(?:USD|EUR)?(?:\s*\([^)]*\))?'
    german_target_pattern = r'(?:Target|Profit)(?:\s*Target)?:\s*(\d+[,.]\d*)\s*(?:USD|EUR)?'

    # Additional patterns for German format with different separators
    german_entry_pattern2 = r'Entry\s*(?:Point|Price)?:\s*(\d+)[.,](\d+)\s*(?:USD|EUR)?(?:\s*\([^)]*\))?'
    german_target_pattern2 = r'(?:Target|Profit)(?:\s*Target)?:\s*(\d+)[.,](\d+)\s*(?:USD|EUR)?'

    # Try all patterns
    entry_match = re.search(entry_pattern, analysis, re.IGNORECASE)
    target_match = re.search(target_pattern, analysis, re.IGNORECASE)

    # If entry/target not found, try buy/sell format
    if not entry_match:
        entry_match = re.search(buy_pattern, analysis, re.IGNORECASE)

    # If still not found, try German format
    if not entry_match:
        # Try first German pattern
        german_entry = re.search(german_entry_pattern, analysis, re.IGNORECASE)
        if german_entry:
            # Convert German number format (comma as decimal separator) to US format
            try:
                entry_value = german_entry.group(1).replace(',', '.')
                # Create a simple object with a group method that returns the value
                class EntryMatch:
                    def group(self, _):
                        return entry_value
                entry_match = EntryMatch()
                logger.info(f"Found German format entry price: {entry_value}")
            except Exception as e:
                logger.error(f"Error parsing German entry price (pattern 1): {e}")

        # If still not found, try second German pattern
        if not entry_match:
            german_entry2 = re.search(german_entry_pattern2, analysis, re.IGNORECASE)
            if german_entry2:
                try:
                    # Combine the integer and decimal parts
                    entry_value = f"{german_entry2.group(1)}.{german_entry2.group(2)}"
                    # Create a simple object with a group method that returns the value
                    class EntryMatch:
                        def group(self, _):
                            return entry_value
                    entry_match = EntryMatch()
                    logger.info(f"Found German format entry price (pattern 2): {entry_value}")
                except Exception as e:
                    logger.error(f"Error parsing German entry price (pattern 2): {e}")

    if not target_match:
        target_match = re.search(sell_pattern, analysis, re.IGNORECASE)

    # If still not found, try German format
    if not target_match:
        # Try first German pattern
        german_target = re.search(german_target_pattern, analysis, re.IGNORECASE)
        if german_target:
            # Convert German number format (comma as decimal separator) to US format
            try:
                target_value = german_target.group(1).replace(',', '.')
                # Create a simple object with a group method that returns the value
                class TargetMatch:
                    def group(self, _):
                        return target_value
                target_match = TargetMatch()
                logger.info(f"Found German format target price: {target_value}")
            except Exception as e:
                logger.error(f"Error parsing German target price (pattern 1): {e}")

        # If still not found, try second German pattern
        if not target_match:
            german_target2 = re.search(german_target_pattern2, analysis, re.IGNORECASE)
            if german_target2:
                try:
                    # Combine the integer and decimal parts
                    target_value = f"{german_target2.group(1)}.{german_target2.group(2)}"
                    # Create a simple object with a group method that returns the value
                    class TargetMatch:
                        def group(self, _):
                            return target_value
                    target_match = TargetMatch()
                    logger.info(f"Found German format target price (pattern 2): {target_value}")
                except Exception as e:
                    logger.error(f"Error parsing German target price (pattern 2): {e}")

    if entry_match or target_match:
        logger.info(f"Found price targets: Entry={entry_match.group(1) if entry_match else 'None'}, Target={target_match.group(1) if target_match else 'None'}")

    entry_price = None
    target_price = None

    if entry_match and target_match:
        try:
            entry_price = float(entry_match.group(1))
            target_price = float(target_match.group(1))
            entry_targets = f"Entry: ${entry_price:.2f}, Target: ${target_price:.2f}"
            logger.info(f"Successfully extracted entry price: ${entry_price:.2f}, target price: ${target_price:.2f}")
        except Exception as e:
            logger.error(f"Error converting entry/target prices to float: {e}")

    # Format the analysis for display
    if "TICKER:" in analysis and "\n" in analysis:
        analysis_text = analysis[analysis.find("\n")+1:].strip()

    # Remove the "Signal: STATUS - RISK" line from the analysis
    analysis_text = re.sub(r'Signal:\s*(BUY|SELL|NEUTRAL BUY|NEUTRAL SELL)\s*-\s*([ABC])', '', analysis_text)

    # Remove any existing "TICKER - STATUS - RISK" lines from the analysis
    analysis_text = re.sub(r'([A-Z]{1,6})\s*-\s*(BUY|SELL|NEUTRAL BUY|NEUTRAL SELL)\s*-\s*([ABC])', '', analysis_text)

    # Clean up any double newlines that might have been created
    analysis_text = re.sub(r'\n\s*\n\s*\n', '\n\n', analysis_text)

    # Clean up any trailing whitespace and newlines
    analysis_text = analysis_text.rstrip()

    formatted_analysis = f"**{company_name} - {ticker_symbol}**\n\n{analysis_text}"

    if entry_targets:
        formatted_analysis += f"\n\n{entry_targets}"

    # Look for the ticker-status-risk format at the end of the analysis
    # This will be our definitive source for both status and risk
    ticker_status_risk_pattern = r'([A-Z]{1,6})\s*-\s*(BUY|SELL|NEUTRAL BUY|NEUTRAL SELL)\s*-\s*([ABC])'
    ticker_status_risk_match = re.search(ticker_status_risk_pattern, analysis, re.IGNORECASE)

    # If we found the ticker-status-risk pattern, use it as the definitive source
    if ticker_status_risk_match:
        ticker_name = ticker_status_risk_match.group(1).upper()
        ticker_status = ticker_status_risk_match.group(2).upper()
        ticker_risk = ticker_status_risk_match.group(3).upper()

        logger.info(f"Using status and risk from ticker format: {ticker_name} - {ticker_status} - {ticker_risk}")

        # Override the previously determined status and risk class
        stock_status = ticker_status
        risk_class = ticker_risk

        # Re-map to internal status
        status_mapping = {
            "BUY": "buy",
            "NEUTRAL BUY": "neutral_bullish",
            "NEUTRAL BULLISH": "neutral_bullish",
            "NEUTRAL SELL": "neutral_bearish",
            "NEUTRAL BEARISH": "neutral_bearish",
            "SELL": "sell"
        }
        internal_status = status_mapping.get(stock_status, "neutral_bullish")
        logger.info(f"Remapped status to internal status: {internal_status}")
    else:
        # If we didn't find the ticker-status-risk pattern, look for the Signal pattern
        signal_pattern = r'Signal:\s*(BUY|SELL|NEUTRAL BUY|NEUTRAL SELL)\s*-\s*([ABC])'
        signal_match = re.search(signal_pattern, analysis, re.IGNORECASE)

        if signal_match:
            signal_status = signal_match.group(1).upper()
            signal_risk = signal_match.group(2).upper()
            logger.info(f"Using signal from analysis: {signal_status} - {signal_risk}")

            # Override the previously determined status and risk class
            stock_status = signal_status
            risk_class = signal_risk

            # Re-map to internal status
            status_mapping = {
                "BUY": "buy",
                "NEUTRAL BUY": "neutral_bullish",
                "NEUTRAL BULLISH": "neutral_bullish",
                "NEUTRAL SELL": "neutral_bearish",
                "NEUTRAL BEARISH": "neutral_bearish",
                "SELL": "sell"
            }
            internal_status = status_mapping.get(stock_status, "neutral_bullish")
            logger.info(f"Remapped status to internal status: {internal_status}")

    # Add the ticker-status-risk format at the end for proper wishlist updating
    # Use the same status and risk class as determined above
    status_text = {
        "buy": "BUY",
        "neutral_bullish": "NEUTRAL BUY",
        "neutral_bearish": "NEUTRAL SELL",
        "sell": "SELL"
    }.get(internal_status, "NEUTRAL BUY")

    # Make sure the formatted analysis doesn't already have a ticker-status-risk line
    # This is a safety check, even though we already removed these lines from analysis_text
    lines = formatted_analysis.split('\n')
    filtered_lines = []
    for line in lines:
        if not re.match(r'^[A-Z]{1,6}\s*-\s*(NEUTRAL\s*BUY|NEUTRAL\s*SELL|BUY|SELL)\s*-\s*[ABC]$', line.strip(), re.IGNORECASE):
            filtered_lines.append(line)

    formatted_analysis = '\n'.join(filtered_lines)

    # Final validation of ticker symbol
    if not re.match(r'^[A-Z]{1,6}$', ticker_symbol):
        logger.warning(f"Invalid ticker symbol format: {ticker_symbol}. Using UNKNOWN instead.")
        ticker_symbol = "UNKNOWN"

    # Add the single, consistent ticker-status-risk format at the end
    formatted_analysis += f"\n\n{ticker_symbol} - {status_text} - {risk_class}"
    logger.info(f"Added single ticker-status-risk format: {ticker_symbol} - {status_text} - {risk_class}")

    # Ensure we have entry and target prices in the result
    result = {
        "formatted_analysis": formatted_analysis,
        "ticker": ticker_symbol,
        "company_name": company_name,
        "status": internal_status,
        "risk_class": risk_class,
        "raw_analysis": analysis,
        "entry_price": entry_price,  # Use the extracted and converted entry_price
        "target_price": target_price,  # Use the extracted and converted target_price
        "original_analysis": analysis  # Store the original analysis text for reference
    }

    # Log the result for debugging
    logger.info(f"Analysis result for {ticker_symbol}: status={internal_status}, risk={risk_class}, entry=${entry_price}, target=${target_price}")

    return result

# Message processing functions
async def process_bot_message(content):
    """Process a message for ticker symbols and sentiment analysis"""
    logger.info(f"Processing message content: {content[:100]}...")

    # First, check for the TICKER - STATUS - RISK format at the end of the message
    lines = [line.strip() for line in content.split('\n') if line.strip()]
    if lines:
        last_line = lines[-1]
        ticker_status_risk_match = re.match(r'^([A-Z]{1,6})\s*-\s*(NEUTRAL\s*BUY|NEUTRAL\s*SELL|BUY|SELL|BULLISH|BEARISH|NEUTRAL\s*BULLISH|NEUTRAL\s*BEARISH)\s*-\s*([ABC])$', last_line, re.IGNORECASE)

        if ticker_status_risk_match:
            ticker = ticker_status_risk_match.group(1).strip()
            status_text = ticker_status_risk_match.group(2).strip().upper()
            risk_profile = ticker_status_risk_match.group(3).strip().upper()

            logger.info(f"Found TICKER-STATUS-RISK format in last line: {ticker} - {status_text} - {risk_profile}")

            # Map status to internal format
            status_mapping = {
                "BUY": "buy",
                "BULLISH": "buy",
                "NEUTRAL BUY": "neutral_bullish",
                "NEUTRAL BULLISH": "neutral_bullish",
                "NEUTRAL BEARISH": "neutral_bearish",
                "NEUTRAL SELL": "neutral_bearish",
                "BEARISH": "sell",
                "SELL": "sell"
            }

            explicit_status = status_mapping.get(status_text, "neutral_bullish")

            # Extract entry point and profit target from the content
            entry_point = None
            profit_target = None

            # Extract entry point and target prices from the analysis with highly flexible patterns
            # This pattern will match various formats like:
            # - Entry Point: 28.00 USD
            # - Entry Point: $28.00
            # - Entry: 28.00
            # - Entry Price: 28,00 EUR
            # - Entry Point: 28.00 (with comments)
            entry_pattern = r'(?:Entry|Buy)(?:\s*(?:Point|Price))?:?\s*(?:\$)?(\d+[.,]\d+)(?:\s*(?:USD|EUR))?(?:\s*\([^)]*\))?'

            # This pattern will match various formats like:
            # - Profit Target: 29.50 USD
            # - Target: $29.50
            # - Profit Target: 29,50 EUR
            # - Target Price: 29.50
            target_pattern = r'(?:Profit|Target)(?:\s*(?:Target|Price))?:?\s*(?:\$)?(\d+[.,]\d+)(?:\s*(?:USD|EUR))?(?:\s*\([^)]*\))?'

            # Log the content for debugging
            logger.info(f"Analyzing content for {ticker} price targets: {content[:200]}...")

            # Log the patterns being used
            logger.info(f"Using entry pattern: {entry_pattern}")
            logger.info(f"Using target pattern: {target_pattern}")

            entry_match = re.search(entry_pattern, content, re.IGNORECASE)
            target_match = re.search(target_pattern, content, re.IGNORECASE)

            if entry_match:
                logger.info(f"Found entry match for {ticker}: {entry_match.group(0)}")
            else:
                logger.warning(f"No entry match found for {ticker}")

            if target_match:
                logger.info(f"Found target match for {ticker}: {target_match.group(0)}")
            else:
                logger.warning(f"No target match found for {ticker}")

            if entry_match and target_match:
                try:
                    # Handle both US and German number formats
                    entry_value = entry_match.group(1).replace(',', '.')
                    target_value = target_match.group(1).replace(',', '.')
                    entry_point = float(entry_value)
                    profit_target = float(target_value)
                    logger.info(f"Successfully extracted price targets for {ticker}: Entry={entry_point}, Target={profit_target}")
                except Exception as e:
                    logger.error(f"Error parsing extracted price targets for {ticker}: {e}")
                    # Log the actual values that caused the error
                    if entry_match:
                        logger.error(f"Entry value that caused error: {entry_match.group(1)}")
                    if target_match:
                        logger.error(f"Target value that caused error: {target_match.group(1)}")
            else:
                logger.warning(f"Could not extract both price targets for {ticker} using primary patterns, trying fallback methods")

                # First priority: Look for values in the "Entry Points und Profit Targets" section
                # This is the most reliable source for entry and target prices (section 4)
                entry_points_section_pattern = r'Entry\s*Points\s*und\s*Profit\s*Targets:[\s\S]*?Entry\s*Point:\s*(?:\$)?(\d+[.,]\d+)(?:\s*USD)?[\s\S]*?Profit\s*Target:\s*(?:\$)?(\d+[.,]\d+)(?:\s*USD)?'

                # Special pattern for the format in the GME example
                klare_entry_pattern = r'(?:Klare\s*)?Entry\s*Points?(?:\s*und\s*Profit\s*Targets?)?:(?:[\s\S]*?)Entry\s*Point:\s*(\d+[.,]\d+)(?:\s*USD)?'
                klare_profit_pattern = r'(?:Klare\s*)?(?:Entry\s*Points?\s*und\s*)?Profit\s*Targets?:(?:[\s\S]*?)(?:Profit\s*Target|Target):\s*(\d+[.,]\d+)(?:\s*USD)?'

                # Even more specific pattern for the exact GME format
                gme_entry_pattern = r'Entry\s*Point:\s*(\d+\.\d+)\s*USD'
                gme_profit_pattern = r'Profit\s*Target:\s*(\d+\.\d+)\s*USD'

                # First priority: Try to extract from the "Entry Points und Profit Targets" section
                entry_points_section_match = re.search(entry_points_section_pattern, content, re.IGNORECASE)

                if entry_points_section_match:
                    try:
                        entry_value = entry_points_section_match.group(1).replace(',', '.')
                        target_value = entry_points_section_match.group(2).replace(',', '.')
                        entry_point = float(entry_value)
                        profit_target = float(target_value)
                        logger.info(f"Extracted price targets from Entry Points und Profit Targets section for {ticker}: Entry={entry_point}, Target={profit_target}")
                    except Exception as e:
                        logger.error(f"Error parsing Entry Points und Profit Targets section values for {ticker}: {e}")

                # If section pattern didn't work, try the exact GME format
                if entry_point is None or profit_target is None:
                    gme_entry_match = re.search(gme_entry_pattern, content, re.IGNORECASE)
                    gme_profit_match = re.search(gme_profit_pattern, content, re.IGNORECASE)

                    if gme_entry_match and gme_profit_match:
                        try:
                            entry_value = gme_entry_match.group(1).replace(',', '.')
                            target_value = gme_profit_match.group(1).replace(',', '.')
                            entry_point = float(entry_value)
                            profit_target = float(target_value)
                            logger.info(f"Extracted price targets from exact GME format for {ticker}: Entry={entry_point}, Target={profit_target}")
                        except Exception as e:
                            logger.error(f"Error parsing exact GME format values for {ticker}: {e}")

                # If exact GME format didn't work, try the more general special patterns
                if entry_point is None or profit_target is None:
                    klare_entry_match = re.search(klare_entry_pattern, content, re.IGNORECASE)
                    klare_profit_match = re.search(klare_profit_pattern, content, re.IGNORECASE)

                    if klare_entry_match and klare_profit_match:
                        try:
                            entry_value = klare_entry_match.group(1).replace(',', '.')
                            target_value = klare_profit_match.group(1).replace(',', '.')
                            entry_point = float(entry_value)
                            profit_target = float(target_value)
                            logger.info(f"Extracted price targets from special format for {ticker}: Entry={entry_point}, Target={profit_target}")
                        except Exception as e:
                            logger.error(f"Error parsing special format values for {ticker}: {e}")

                # Fallback: Look for "Support" and "Resistance" levels as a LAST RESORT
                # Note: This is less reliable as it takes values from section 3 instead of section 4
                if entry_point is None or profit_target is None:
                    logger.warning(f"Could not find entry/target prices in section 4 for {ticker}, falling back to support/resistance from section 3")
                    support_pattern = r'Support:?\s*(?:\$)?(\d+[.,]\d+)'
                    resistance_pattern = r'Resistance:?\s*(?:\$)?(\d+[.,]\d+)'

                    support_match = re.search(support_pattern, content, re.IGNORECASE)
                    resistance_match = re.search(resistance_pattern, content, re.IGNORECASE)

                    if support_match and resistance_match:
                        try:
                            support_value = support_match.group(1).replace(',', '.')
                            resistance_value = resistance_match.group(1).replace(',', '.')
                            entry_point = float(support_value)
                            profit_target = float(resistance_value)
                            logger.info(f"Extracted price targets from support/resistance for {ticker}: Entry={entry_point}, Target={profit_target}")
                            logger.warning(f"Using support/resistance values as fallback for {ticker}. These may not be the intended entry/target prices.")
                        except Exception as e:
                            logger.error(f"Error parsing support/resistance values for {ticker}: {e}")

                # Fallback 2: Look for any numbers in the text and use the first two
                if entry_point is None or profit_target is None:
                    number_pattern = r'\d+[.,]\d+'
                    numbers = re.findall(number_pattern, content)

                    if len(numbers) >= 2:
                        try:
                            # Use the first two numbers found
                            entry_value = numbers[0].replace(',', '.')
                            target_value = numbers[1].replace(',', '.')
                            entry_point = float(entry_value)
                            profit_target = float(target_value)
                            logger.info(f"Extracted price targets from raw numbers for {ticker}: Entry={entry_point}, Target={profit_target}")
                        except Exception as e:
                            logger.error(f"Error parsing raw numbers for {ticker}: {e}")
                    else:
                        logger.warning(f"Could not find enough numbers in the analysis for {ticker}")






            # Process the ticker analysis with the extracted data
            updates_count = await process_ticker_analysis(ticker, explicit_status == "buy", explicit_status == "sell",
                                                         entry_point, profit_target, explicit_status, risk_profile, content)

            logger.info(f"Updated {ticker} in {updates_count} watchlists with status: {explicit_status}, risk: {risk_profile}")
            return updates_count

    # If no TICKER-STATUS-RISK format found, fall back to the regular analysis
    analysis = analyze_message_sentiment(content)
    tickers = analysis["tickers"]
    is_bullish = analysis["is_bullish"]
    is_bearish = analysis["is_bearish"]
    explicit_status = analysis["explicit_status"]
    specific_ticker = analysis["specific_ticker"]
    risk_profile = analysis.get("risk_profile")  # Get risk profile from analysis if available

    logger.info(f"Message sentiment analysis: ticker={specific_ticker}, status={explicit_status}, risk={risk_profile}")

    if specific_ticker and (is_bullish or is_bearish or explicit_status):
        # Extract entry point, profit target, and risk profile if not already set
        entry_point = None
        profit_target = None

        # Extract risk profile if not already set from the TICKER-STATUS-RISK format
        if not risk_profile:
            risk_pattern = r'(?:Risk(?:iko)?(?:\s*Profil)?|Setup(?:\s*zum)?(?:\s*Einstieg)?)(?:\s*:)?\s*([ABC])'
            risk_match = re.search(risk_pattern, content, re.IGNORECASE)

            if risk_match:
                risk_profile = risk_match.group(1).upper()
                logger.info(f"Extracted risk profile from content: {risk_profile}")

        # Extract entry point and profit target
        combined_pattern = r'Entry\s*Point:\s*\$?(\d+\.?\d*).*?(?:Profit|Target).*?\$?(\d+\.?\d*)'
        combined_match = re.search(combined_pattern, content, re.IGNORECASE)

        if combined_match:
            try:
                entry_point = float(combined_match.group(1))
                profit_target = float(combined_match.group(2))
                logger.info(f"Extracted entry point: ${entry_point}, profit target: ${profit_target}")
            except Exception as e:
                logger.error(f"Error parsing combined pattern: {e}")

        # Process the ticker analysis
        updates_count = await process_ticker_analysis(specific_ticker, is_bullish, is_bearish,
                                     entry_point, profit_target, explicit_status, risk_profile, content)

        logger.info(f"Updated {specific_ticker} in {updates_count} watchlists with status: {explicit_status}, risk: {risk_profile}")
        return updates_count

    # Process other tickers found in the message
    elif tickers and (is_bullish or is_bearish or explicit_status):
        total_updates = 0
        for ticker in tickers:
            updates = await process_ticker_analysis(ticker, is_bullish, is_bearish, None, None, explicit_status, None, content)
            total_updates += updates
        return total_updates

    return 0

def analyze_message_sentiment(message_content):
    """Analyze a message for ticker symbols and sentiment"""
    # Check for specific formats at the end of the message: "TICKER - STATUS" or "TICKER - STATUS - RISK"
    non_empty_lines = [line.strip() for line in message_content.split('\n') if line.strip()]
    specific_ticker = None
    explicit_status = None
    risk_profile = None

    if non_empty_lines:
        last_line = non_empty_lines[-1]

        # Check for "TICKER - STATUS - RISK" format
        ticker_status_risk_match = re.match(r'^([A-Z]{1,6})\s*-\s*(NEUTRAL\s*BUY|NEUTRAL\s*SELL|BUY|SELL|BULLISH|BEARISH|NEUTRAL\s*BULLISH|NEUTRAL\s*BEARISH)\s*-\s*([ABC])$', last_line, re.IGNORECASE)
        if ticker_status_risk_match:
            specific_ticker = ticker_status_risk_match.group(1).strip()
            status_text = ticker_status_risk_match.group(2).strip().upper()
            risk_profile = ticker_status_risk_match.group(3).strip().upper()
            logger.info(f"Parsed TICKER-STATUS-RISK format: {specific_ticker} - {status_text} - {risk_profile}")

        # If not found, check for "TICKER - STATUS" format
        elif re.match(r'^[A-Z]{1,6}\s*-\s*(NEUTRAL\s*BUY|NEUTRAL\s*SELL|BUY|SELL|BULLISH|BEARISH|NEUTRAL\s*BULLISH|NEUTRAL\s*BEARISH)$', last_line, re.IGNORECASE):
            parts = re.split(r'\s*-\s*', last_line, 1)
            if len(parts) == 2:
                specific_ticker = parts[0].strip()
                status_text = parts[1].strip().upper()
                logger.info(f"Parsed TICKER-STATUS format: {specific_ticker} - {status_text}")

                status_mapping = {
                    "BUY": "buy",
                    "BULLISH": "buy",
                    "NEUTRAL BUY": "neutral_bullish",
                    "NEUTRAL BULLISH": "neutral_bullish",
                    "NEUTRAL BEARISH": "neutral_bearish",
                    "NEUTRAL SELL": "neutral_bearish",
                    "BEARISH": "sell",
                    "SELL": "sell"
                }

                # Log the status mapping for debugging
                if status_text in status_mapping:
                    logger.info(f"Message analysis: Mapped '{status_text}' to '{status_mapping[status_text]}'")

                if status_text in status_mapping:
                    explicit_status = status_mapping[status_text]
                else:
                    for key, value in status_mapping.items():
                        if key in status_text:
                            explicit_status = value
                            break

    # Extract potential ticker symbols
    tickers = []
    if not specific_ticker:
        ticker_pattern = r'\b[A-Z]{1,5}\b'
        potential_tickers = re.findall(ticker_pattern, message_content)

        common_words = {"A", "I", "AM", "PM", "THE", "AND", "OR", "IF", "IN", "ON", "AT", "TO", "BY", "FOR"}
        tickers = [ticker for ticker in potential_tickers if ticker not in common_words]
    else:
        tickers = [specific_ticker]

    # Determine bullish/bearish sentiment
    is_bullish = False
    is_bearish = False

    if explicit_status:
        is_bullish = explicit_status in ["buy", "neutral_bullish"]
        is_bearish = explicit_status in ["sell", "neutral_bearish"]
    else:
        bullish_keywords = ["bullish", "buy", "long", "up", "increase", "growth", "positive", "strong", "higher", "rise", "gain"]
        bearish_keywords = ["bearish", "sell", "short", "down", "decrease", "decline", "negative", "weak", "lower", "fall", "loss"]

        message_lower = message_content.lower()

        is_bullish = any(keyword in message_lower for keyword in bullish_keywords)
        is_bearish = any(keyword in message_lower for keyword in bearish_keywords)

    return {
        "tickers": tickers,
        "is_bullish": is_bullish,
        "is_bearish": is_bearish,
        "explicit_status": explicit_status,
        "specific_ticker": specific_ticker,
        "risk_profile": risk_profile  # Include the risk profile in the return value
    }

async def process_ticker_analysis(ticker, is_bullish, is_bearish, buy_price=None, sell_price=None, explicit_status=None, risk_profile=None, raw_analysis=None):
    """Process ticker analysis and update all watchlists"""
    logger.info(f"Processing ticker analysis: {ticker}, bullish={is_bullish}, bearish={is_bearish}, status={explicit_status}, risk={risk_profile}")

    # Validate ticker
    if not ticker or not isinstance(ticker, str) or len(ticker) > 6:
        logger.error(f"Invalid ticker: {ticker}")
        return 0

    # Format ticker to ensure it's uppercase
    ticker = ticker.upper().strip()

    # Update sentiment tracking
    update_ticker_sentiment(ticker, is_bullish, is_bearish)

    # Determine status
    if explicit_status:
        new_status = explicit_status
        logger.info(f"Using explicit status for {ticker}: {new_status}")
    else:
        new_status = determine_ticker_status(ticker)
        logger.info(f"Determined status for {ticker}: {new_status}")

    # Update only existing watchlist entries
    data = load_data()
    updates_count = 0

    # Log the current data structure for debugging
    watchlist_count = sum(1 for user_id in data if "watchlist" in data[user_id] and ticker in data[user_id]["watchlist"])
    logger.info(f"Found {ticker} in {watchlist_count} watchlists")

    for user_id in data:
        if "watchlist" in data[user_id]:
            # Only update if ticker is already in the user's watchlist
            if ticker in data[user_id]["watchlist"]:
                old_status = data[user_id]["watchlist"][ticker]["status"]
                old_risk = data[user_id]["watchlist"][ticker].get("risk_profile", "C")

                # Update status
                data[user_id]["watchlist"][ticker]["status"] = new_status

                # Update risk profile if provided
                if risk_profile:
                    data[user_id]["watchlist"][ticker]["risk_profile"] = risk_profile
                    logger.info(f"Updated risk profile for {ticker} from {old_risk} to {risk_profile}")

                # Store the analysis text if available
                # We'll just use the raw_analysis parameter if it's passed in
                if raw_analysis:
                    data[user_id]["watchlist"][ticker]["analysis"] = raw_analysis
                    logger.info(f"Stored raw analysis text for {ticker}")

                # Update price targets if provided
                if buy_price is not None and sell_price is not None:
                    # Debug log before update
                    logger.info(f"Before update for {ticker}: entry_price={data[user_id]['watchlist'][ticker].get('entry_price')}, target_price={data[user_id]['watchlist'][ticker].get('target_price')}")

                    # Update entry and target prices
                    data[user_id]["watchlist"][ticker]["entry_price"] = buy_price
                    data[user_id]["watchlist"][ticker]["target_price"] = sell_price

                    # Debug log after update
                    logger.info(f"After update for {ticker}: entry_price={data[user_id]['watchlist'][ticker].get('entry_price')}, target_price={data[user_id]['watchlist'][ticker].get('target_price')}")

                    logger.info(f"Updated price targets for {ticker}: Entry=${buy_price}, Target=${sell_price}")
                else:
                    # Try to extract entry and target prices from the analysis text
                    extracted_buy = None
                    extracted_sell = None

                    # Initialize these values if they don't exist in the watchlist
                    if "entry_price" not in data[user_id]["watchlist"][ticker]:
                        data[user_id]["watchlist"][ticker]["entry_price"] = None
                    if "target_price" not in data[user_id]["watchlist"][ticker]:
                        data[user_id]["watchlist"][ticker]["target_price"] = None

                    if raw_analysis:
                        # Extract entry point and target prices from the analysis with highly flexible patterns
                        # This pattern will match various formats like:
                        # - Entry Point: 28.00 USD
                        # - Entry Point: $28.00
                        # - Entry: 28.00
                        # - Entry Price: 28,00 EUR
                        # - Entry Point: 28.00 (with comments)
                        entry_pattern = r'(?:Entry|Buy)(?:\s*(?:Point|Price))?:?\s*(?:\$)?(\d+[.,]\d+)(?:\s*(?:USD|EUR))?(?:\s*\([^)]*\))?'

                        # This pattern will match various formats like:
                        # - Profit Target: 29.50 USD
                        # - Target: $29.50
                        # - Profit Target: 29,50 EUR
                        # - Target Price: 29.50
                        target_pattern = r'(?:Profit|Target)(?:\s*(?:Target|Price))?:?\s*(?:\$)?(\d+[.,]\d+)(?:\s*(?:USD|EUR))?(?:\s*\([^)]*\))?'

                        entry_match = re.search(entry_pattern, raw_analysis, re.IGNORECASE)
                        target_match = re.search(target_pattern, raw_analysis, re.IGNORECASE)

                        # Log the raw analysis text for debugging
                        logger.info(f"Analyzing text for {ticker} price targets: {raw_analysis[:200]}...")

                        # Log the patterns being used
                        logger.info(f"Using entry pattern: {entry_pattern}")
                        logger.info(f"Using target pattern: {target_pattern}")

                        if entry_match:
                            logger.info(f"Found entry match for {ticker}: {entry_match.group(0)}")
                        else:
                            logger.warning(f"No entry match found for {ticker}")

                        if target_match:
                            logger.info(f"Found target match for {ticker}: {target_match.group(0)}")
                        else:
                            logger.warning(f"No target match found for {ticker}")

                        if entry_match and target_match:
                            try:
                                # Handle both US and German number formats
                                entry_value = entry_match.group(1).replace(',', '.')
                                target_value = target_match.group(1).replace(',', '.')
                                extracted_buy = float(entry_value)
                                extracted_sell = float(target_value)
                                logger.info(f"Successfully extracted price targets for {ticker}: Entry={extracted_buy}, Target={extracted_sell}")
                            except Exception as e:
                                logger.error(f"Error parsing extracted price targets for {ticker}: {e}")
                                # Log the actual values that caused the error
                                if entry_match:
                                    logger.error(f"Entry value that caused error: {entry_match.group(1)}")
                                if target_match:
                                    logger.error(f"Target value that caused error: {target_match.group(1)}")
                        else:
                            logger.warning(f"Could not extract both price targets for {ticker} using primary patterns, trying fallback methods")

                            # First priority: Look for values in the "Entry Points und Profit Targets" section
                            # This is the most reliable source for entry and target prices (section 4)
                            entry_points_section_pattern = r'Entry\s*Points\s*und\s*Profit\s*Targets:[\s\S]*?Entry\s*Point:\s*(?:\$)?(\d+[.,]\d+)(?:\s*USD)?[\s\S]*?Profit\s*Target:\s*(?:\$)?(\d+[.,]\d+)(?:\s*USD)?'

                            # Special pattern for the format in the GME example
                            klare_entry_pattern = r'(?:Klare\s*)?Entry\s*Points?(?:\s*und\s*Profit\s*Targets?)?:(?:[\s\S]*?)Entry\s*Point:\s*(\d+[.,]\d+)(?:\s*USD)?'
                            klare_profit_pattern = r'(?:Klare\s*)?(?:Entry\s*Points?\s*und\s*)?Profit\s*Targets?:(?:[\s\S]*?)(?:Profit\s*Target|Target):\s*(\d+[.,]\d+)(?:\s*USD)?'

                            # Fallback: Look for "Support" and "Resistance" levels (section 3)
                            support_pattern = r'Support:?\s*(?:\$)?(\d+[.,]\d+)'
                            resistance_pattern = r'Resistance:?\s*(?:\$)?(\d+[.,]\d+)'

                            # Even more specific pattern for the exact GME format
                            gme_entry_pattern = r'Entry\s*Point:\s*(\d+\.\d+)\s*USD'
                            gme_profit_pattern = r'Profit\s*Target:\s*(\d+\.\d+)\s*USD'

                            # First priority: Try to extract from the "Entry Points und Profit Targets" section
                            entry_points_section_match = re.search(entry_points_section_pattern, raw_analysis, re.IGNORECASE)

                            if entry_points_section_match:
                                try:
                                    entry_value = entry_points_section_match.group(1).replace(',', '.')
                                    target_value = entry_points_section_match.group(2).replace(',', '.')
                                    extracted_buy = float(entry_value)
                                    extracted_sell = float(target_value)
                                    logger.info(f"Extracted price targets from Entry Points und Profit Targets section for {ticker}: Entry={extracted_buy}, Target={extracted_sell}")
                                except Exception as e:
                                    logger.error(f"Error parsing Entry Points und Profit Targets section values for {ticker}: {e}")

                            # If section pattern didn't work, try the exact GME format
                            if extracted_buy is None or extracted_sell is None:
                                gme_entry_match = re.search(gme_entry_pattern, raw_analysis, re.IGNORECASE)
                                gme_profit_match = re.search(gme_profit_pattern, raw_analysis, re.IGNORECASE)

                                if gme_entry_match and gme_profit_match:
                                    try:
                                        entry_value = gme_entry_match.group(1).replace(',', '.')
                                        target_value = gme_profit_match.group(1).replace(',', '.')
                                        extracted_buy = float(entry_value)
                                        extracted_sell = float(target_value)
                                        logger.info(f"Extracted price targets from exact GME format for {ticker}: Entry={extracted_buy}, Target={extracted_sell}")
                                    except Exception as e:
                                        logger.error(f"Error parsing exact GME format values for {ticker}: {e}")

                            # If exact GME format didn't work, try the more general special patterns
                            if extracted_buy is None or extracted_sell is None:
                                klare_entry_match = re.search(klare_entry_pattern, raw_analysis, re.IGNORECASE)
                                klare_profit_match = re.search(klare_profit_pattern, raw_analysis, re.IGNORECASE)

                                if klare_entry_match and klare_profit_match:
                                    try:
                                        entry_value = klare_entry_match.group(1).replace(',', '.')
                                        target_value = klare_profit_match.group(1).replace(',', '.')
                                        extracted_buy = float(entry_value)
                                        extracted_sell = float(target_value)
                                        logger.info(f"Extracted price targets from special format for {ticker}: Entry={extracted_buy}, Target={extracted_sell}")
                                    except Exception as e:
                                        logger.error(f"Error parsing special format values for {ticker}: {e}")

                            # If special patterns didn't work, try support/resistance as a LAST RESORT
                            # Note: This is less reliable as it takes values from section 3 instead of section 4
                            if extracted_buy is None or extracted_sell is None:
                                logger.warning(f"Could not find entry/target prices in section 4 for {ticker}, falling back to support/resistance from section 3")
                                support_match = re.search(support_pattern, raw_analysis, re.IGNORECASE)
                                resistance_match = re.search(resistance_pattern, raw_analysis, re.IGNORECASE)

                                if support_match and resistance_match:
                                    try:
                                        support_value = support_match.group(1).replace(',', '.')
                                        resistance_value = resistance_match.group(1).replace(',', '.')
                                        extracted_buy = float(support_value)
                                        extracted_sell = float(resistance_value)
                                        logger.info(f"Extracted price targets from support/resistance for {ticker}: Entry={extracted_buy}, Target={extracted_sell}")
                                        logger.warning(f"Using support/resistance values as fallback for {ticker}. These may not be the intended entry/target prices.")
                                    except Exception as e:
                                        logger.error(f"Error parsing support/resistance values for {ticker}: {e}")

                            # Fallback 2: Look for any numbers in the text and use the first two
                            if extracted_buy is None or extracted_sell is None:
                                number_pattern = r'\d+[.,]\d+'
                                numbers = re.findall(number_pattern, raw_analysis)

                                if len(numbers) >= 2:
                                    try:
                                        # Use the first two numbers found
                                        entry_value = numbers[0].replace(',', '.')
                                        target_value = numbers[1].replace(',', '.')
                                        extracted_buy = float(entry_value)
                                        extracted_sell = float(target_value)
                                        logger.info(f"Extracted price targets from raw numbers for {ticker}: Entry={extracted_buy}, Target={extracted_sell}")
                                    except Exception as e:
                                        logger.error(f"Error parsing raw numbers for {ticker}: {e}")
                                else:
                                    logger.warning(f"Could not find enough numbers in the analysis for {ticker}")

                    if extracted_buy is not None and extracted_sell is not None:
                        # Debug log before update
                        logger.info(f"Before extracted update for {ticker}: entry_price={data[user_id]['watchlist'][ticker].get('entry_price')}, target_price={data[user_id]['watchlist'][ticker].get('target_price')}")

                        # Update entry and target prices
                        data[user_id]["watchlist"][ticker]["entry_price"] = extracted_buy
                        data[user_id]["watchlist"][ticker]["target_price"] = extracted_sell

                        # Debug log after update
                        logger.info(f"After extracted update for {ticker}: entry_price={data[user_id]['watchlist'][ticker].get('entry_price')}, target_price={data[user_id]['watchlist'][ticker].get('target_price')}")

                        logger.info(f"Extracted and updated price targets for {ticker}: Entry=${extracted_buy}, Target=${extracted_sell}")
                    else:
                        logger.warning(f"Could not extract price targets for {ticker} after trying all methods")

                updates_count += 1
                logger.info(f"Updated {ticker} in {user_id}'s watchlist from {old_status} to {new_status}")

    # Save the updated data
    if updates_count > 0:
        save_data(data)
        logger.info(f"Successfully updated {ticker} in {updates_count} watchlists")
    else:
        logger.warning(f"No watchlists were updated for {ticker}. Check if the ticker exists in any watchlists.")

    return updates_count

def update_ticker_sentiment(ticker, is_bullish, is_bearish):
    """Update sentiment tracking for a ticker"""
    if ticker not in TICKER_SENTIMENT:
        TICKER_SENTIMENT[ticker] = {"bullish": 0, "bearish": 0}

    if is_bullish:
        TICKER_SENTIMENT[ticker]["bullish"] += 1

    if is_bearish:
        TICKER_SENTIMENT[ticker]["bearish"] += 1

    return TICKER_SENTIMENT[ticker]

def determine_ticker_status(ticker):
    """Determine ticker status based on sentiment tracking"""
    if ticker not in TICKER_SENTIMENT:
        return "neutral_bullish"  # Default status

    sentiment = TICKER_SENTIMENT[ticker]

    if sentiment["bullish"] >= BULLISH_THRESHOLD and sentiment["bearish"] == 0:
        return "buy"
    elif sentiment["bullish"] > sentiment["bearish"]:
        return "neutral_bullish"
    elif sentiment["bearish"] > sentiment["bullish"]:
        return "neutral_bearish"
    elif sentiment["bearish"] >= BEARISH_THRESHOLD and sentiment["bullish"] == 0:
        return "sell"
    else:
        return "neutral_bullish"  # Default status

# Wishlist management functions
def add_to_watchlist(user_id, ticker, status=None, alert_low=None, alert_high=None, risk_profile=None):
    """Add a ticker to a user's watchlist"""
    data = load_data()

    # Initialize user data if not exists
    if str(user_id) not in data:
        data[str(user_id)] = {
            "trades": [],
            "holdings": {},
            "avg_price": {},
            "watchlist": {}
        }

    user_data = data[str(user_id)]

    # Initialize watchlist if not exists
    if "watchlist" not in user_data:
        user_data["watchlist"] = {}

    # Format ticker
    ticker = ticker.upper().strip()

    # Set default status if not provided
    if status is None or status not in ["buy", "neutral_bullish", "neutral_bearish", "sell"]:
        status = "neutral_bullish"  # Default status

    # Set default risk profile if not provided
    if risk_profile is None or risk_profile not in ["A", "B", "C"]:
        risk_profile = "B"  # Default risk profile (medium risk)

    # Add to watchlist
    user_data["watchlist"][ticker] = {
        "status": status,
        "risk_profile": risk_profile,
        "entry_price": alert_low,  # Use alert_low as entry price
        "target_price": alert_high,  # Use alert_high as target price
        "added_at": datetime.datetime.now().isoformat(),
        "analysis": "",  # Empty analysis initially
        "last_updated": datetime.datetime.now().isoformat()
    }

    # Save updated data
    save_data(data)
    return True

def remove_from_watchlist(user_id, ticker):
    """Remove a ticker from a user's watchlist"""
    data = load_data()

    if str(user_id) not in data or "watchlist" not in data[str(user_id)]:
        return False

    # Format ticker
    ticker = ticker.upper().strip()

    # Remove from watchlist if exists
    if ticker in data[str(user_id)]["watchlist"]:
        del data[str(user_id)]["watchlist"][ticker]
        save_data(data)
        return True

    return False

def update_watchlist_alerts(user_id, ticker, alert_low, alert_high):
    """Update the alert zones of a ticker in a user's watchlist"""
    data = load_data()

    if str(user_id) not in data or "watchlist" not in data[str(user_id)]:
        return False

    # Format ticker
    ticker = ticker.upper().strip()

    # Update alert zones if ticker exists in watchlist
    if ticker in data[str(user_id)]["watchlist"]:
        # Update entry and target prices
        data[str(user_id)]["watchlist"][ticker]["entry_price"] = alert_low
        data[str(user_id)]["watchlist"][ticker]["target_price"] = alert_high

        # Update last updated timestamp
        data[str(user_id)]["watchlist"][ticker]["last_updated"] = datetime.datetime.now().isoformat()

        save_data(data)
        logger.info(f"Updated price targets for {ticker}: Entry=${alert_low}, Target=${alert_high}")
        return True

    return False

def get_watchlist(user_id):
    """Get a user's watchlist"""
    data = load_data()

    if str(user_id) not in data or "watchlist" not in data[str(user_id)]:
        return {}

    return data[str(user_id)]["watchlist"]

async def fetch_price(ticker):
    """Fetch current price for a ticker - simplified version with no retries"""
    global PRICE_CACHE

    # Format ticker
    ticker = ticker.upper().strip()

    # Check cache first (simple 15-minute cache)
    current_time = datetime.datetime.now().timestamp()
    cache_expiry = 15 * 60  # 15 minutes

    if ticker in PRICE_CACHE:
        cache_entry = PRICE_CACHE[ticker]
        if current_time - cache_entry["timestamp"] < cache_expiry:
            logger.info(f"Using cached price for {ticker}: ${cache_entry['price']:.2f}")
            return cache_entry["price"]

    # Simple one-try approach with yfinance
    try:
        # Create ticker object
        ticker_obj = yf.Ticker(ticker)

        # Try to get data from ticker.info first (most reliable method)
        try:
            ticker_info = ticker_obj.info
            if 'regularMarketPrice' in ticker_info and ticker_info['regularMarketPrice'] is not None:
                price = ticker_info['regularMarketPrice']
                # Update cache
                PRICE_CACHE[ticker] = {
                    "price": price,
                    "timestamp": current_time
                }
                logger.info(f"Got price from ticker.info for {ticker}: ${price:.2f}")
                return price
        except Exception as info_error:
            logger.warning(f"Failed to get info for {ticker}: {info_error}")

        # Fall back to history method
        try:
            data = ticker_obj.history(period="1d")

            if not data.empty:
                price = data['Close'].iloc[-1]
                # Update cache
                PRICE_CACHE[ticker] = {
                    "price": price,
                    "timestamp": current_time
                }
                logger.info(f"Successfully fetched price for {ticker} using yfinance history: ${price:.2f}")
                return price
            else:
                logger.error(f"Empty data returned for {ticker} from yfinance")
        except Exception as history_error:
            logger.error(f"History method failed for {ticker}: {history_error}")

    except Exception as e:
        logger.error(f"Error fetching price for {ticker}: {e}")

    # If we get here, all methods failed
    logger.error(f"Failed to get price for {ticker}")

    # Return cached price even if expired as last resort
    if ticker in PRICE_CACHE:
        logger.warning(f"Using expired cached price for {ticker} as fallback")
        return PRICE_CACHE[ticker]["price"]

    return None

async def fetch_price_alternative(ticker):
    """Alternative method to fetch price when Yahoo Finance is rate limited"""
    ticker = ticker.upper().strip()
    current_time = datetime.datetime.now().timestamp()

    # Try multiple alternative methods
    methods = [
        fetch_price_yahoo_api,
        fetch_price_async_http
    ]

    for method in methods:
        try:
            price = await method(ticker)
            if price is not None:
                # Update cache
                PRICE_CACHE[ticker] = {
                    "price": price,
                    "timestamp": current_time
                }
                logger.info(f"Successfully fetched price for {ticker} using alternative method: ${price:.2f}")
                return price
        except Exception as e:
            logger.error(f"Error in alternative price fetch for {ticker} using {method.__name__}: {e}")

    # Last resort: Try direct web scraping
    try:
        price = await scrape_stock_price(ticker)
        if price is not None:
            # Update cache
            PRICE_CACHE[ticker] = {
                "price": price,
                "timestamp": current_time
            }
            logger.info(f"Successfully scraped price for {ticker}: ${price:.2f}")
            return price
    except Exception as e:
        logger.error(f"Error scraping price for {ticker}: {e}")

    logger.error(f"All price fetch methods failed for {ticker}")

    # Return cached price even if expired as last resort
    if ticker in PRICE_CACHE:
        logger.warning(f"Using expired cached price for {ticker} as fallback")
        return PRICE_CACHE[ticker]["price"]

    return None

async def scrape_stock_price(ticker):
    """Scrape stock price directly from financial websites as a last resort"""
    import re
    import random

    # List of realistic user agents to rotate
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:108.0) Gecko/20100101 Firefox/108.0',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/108.0.1462.76',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/********'
    ]

    # List of sites to try - prioritizing those that are less likely to block
    sites = [
        # Google Finance is usually the most reliable
        {
            'url': f'https://www.google.com/finance/quote/{ticker}',
            'pattern': r'data-last-price="([0-9,.]+)"'
        },
        {
            'url': f'https://www.google.com/finance/quote/{ticker}:NYSE',
            'pattern': r'data-last-price="([0-9,.]+)"'
        },
        {
            'url': f'https://www.google.com/finance/quote/{ticker}:NASDAQ',
            'pattern': r'data-last-price="([0-9,.]+)"'
        },
        # MarketWatch is also fairly reliable
        {
            'url': f'https://www.marketwatch.com/investing/stock/{ticker}',
            'pattern': r'<bg-quote[^>]*>([0-9,.]+)</bg-quote>'
        },
        {
            'url': f'https://www.marketwatch.com/investing/stock/{ticker}',
            'pattern': r'<bg-quote.*?price="([0-9,.]+)"'
        },
        # CNBC can work well
        {
            'url': f'https://www.cnbc.com/quotes/{ticker}',
            'pattern': r'class="last.*?>([0-9,.]+)<'
        },
        # Try Yahoo Finance last as it's most likely to block
        {
            'url': f'https://finance.yahoo.com/quote/{ticker}',
            'pattern': r'data-symbol="{0}".*?value="([0-9,.]+)"'.format(ticker)
        },
        {
            'url': f'https://finance.yahoo.com/quote/{ticker}',
            'pattern': r'fin-streamer.*?value="([0-9,.]+)"'
        }
    ]

    # Try additional sources that might be less likely to block
    additional_sites = [
        {
            'url': f'https://finviz.com/quote.ashx?t={ticker.lower()}',
            'pattern': r'<b>([0-9,.]+)</b>'
        },
        {
            'url': f'https://www.tradingview.com/symbols/{ticker}/',
            'pattern': r'<span class="tv-symbol-price-quote__value.*?>([0-9,.]+)</span>'
        },
        {
            'url': f'https://www.wsj.com/market-data/quotes/{ticker}',
            'pattern': r'<span id="quote_val">([0-9,.]+)</span>'
        },
        {
            'url': f'https://seekingalpha.com/symbol/{ticker}',
            'pattern': r'<span.*?data-test-id="symbol-price">([0-9,.]+)</span>'
        }
    ]

    # Combine all sites
    all_sites = sites + additional_sites

    for site in all_sites:
        try:
            # Use a random user agent for each request
            headers = {
                'User-Agent': random.choice(user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Referer': 'https://www.google.com/',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0'
            }

            # Add a small delay to avoid being detected as a bot
            await asyncio.sleep(random.uniform(0.5, 1.5))

            async with aiohttp.ClientSession() as session:
                async with session.get(site['url'], headers=headers, timeout=15) as response:
                    if response.status == 200:
                        html = await response.text()

                        # Try multiple patterns for each site
                        patterns = [site['pattern']]

                        # Add some generic patterns that might work across sites
                        generic_patterns = [
                            r'class="price.*?>([0-9,.]+)<',
                            r'id="price.*?>([0-9,.]+)<',
                            r'data-price="([0-9,.]+)"',
                            r'price.*?>\$?([0-9,.]+)<',
                            r'value.*?>\$?([0-9,.]+)<'
                        ]

                        all_patterns = patterns + generic_patterns

                        for pattern in all_patterns:
                            match = re.search(pattern, html)
                            if match:
                                price_str = match.group(1).replace(',', '')
                                try:
                                    price = float(price_str)
                                    logger.info(f"Successfully scraped price for {ticker} from {site['url']}: ${price:.2f}")
                                    return price
                                except ValueError:
                                    logger.warning(f"Found invalid price format for {ticker} from {site['url']}: {price_str}")
                                    continue
                    else:
                        logger.warning(f"HTTP {response.status} when scraping {ticker} from {site['url']}")
        except Exception as e:
            logger.warning(f"Error scraping {site['url']} for {ticker}: {e}")
            continue

    # Last resort: Try to get price from a stock API
    try:
        # Try Alpha Vantage with demo key (limited requests)
        url = f"https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol={ticker}&apikey=demo"
        headers = {'User-Agent': random.choice(user_agents)}

        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'Global Quote' in data and '05. price' in data['Global Quote']:
                        price_str = data['Global Quote']['05. price']
                        price = float(price_str)
                        logger.info(f"Successfully fetched price for {ticker} from Alpha Vantage: ${price:.2f}")
                        return price
    except Exception as e:
        logger.warning(f"Error fetching from Alpha Vantage for {ticker}: {e}")

    return None

async def fetch_price_yahoo_api(ticker):
    """Fetch price using Yahoo Finance API directly with retries"""
    import random
    import time

    max_retries = 3
    retry_delay = 2  # seconds

    # List of realistic user agents to rotate
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:108.0) Gecko/20100101 Firefox/108.0',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/108.0.1462.76',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/********'
    ]

    # Use a random user agent for each request
    headers = {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://www.google.com/search?q=' + ticker + '+stock',
        'Origin': 'https://www.google.com',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'cross-site',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
    }

    for attempt in range(max_retries):
        try:
            # Add a small random delay to avoid being detected as a bot
            await asyncio.sleep(random.uniform(0.5, 1.5))

            # Use a new random user agent for each attempt
            headers['User-Agent'] = random.choice(user_agents)

            # Try different Yahoo Finance endpoints
            urls = [
                f"https://query1.finance.yahoo.com/v8/finance/chart/{ticker}",
                f"https://query2.finance.yahoo.com/v8/finance/chart/{ticker}",
                f"https://query1.finance.yahoo.com/v7/finance/quote?symbols={ticker}",
                f"https://finance.yahoo.com/quote/{ticker}/history?period1={int(time.time())-86400}&period2={int(time.time())}&interval=1d&filter=history&frequency=1d"
            ]

            # Shuffle the URLs to make the pattern less predictable
            random.shuffle(urls)

            for url in urls:
                try:
                    req = urllib.request.Request(url, headers=headers)
                    response = urllib.request.urlopen(req, timeout=10)
                    data = json.loads(response.read().decode())

                    # Handle v8 chart API
                    if 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                        result = data['chart']['result'][0]
                        if 'meta' in result and 'regularMarketPrice' in result['meta']:
                            return result['meta']['regularMarketPrice']

                    # Handle v7 quote API
                    if 'quoteResponse' in data and 'result' in data['quoteResponse'] and data['quoteResponse']['result']:
                        result = data['quoteResponse']['result'][0]
                        if 'regularMarketPrice' in result:
                            return result['regularMarketPrice']

                except Exception as url_error:
                    logger.warning(f"Error with URL {url} for {ticker}: {url_error}")
                    continue

            # If we get here, all URLs failed
            logger.warning(f"All Yahoo Finance URLs failed for {ticker}, attempt {attempt+1}/{max_retries}")

            if attempt < max_retries - 1:
                # Wait before retrying with exponential backoff
                await asyncio.sleep(retry_delay * (2 ** attempt))

        except Exception as e:
            logger.error(f"Yahoo API fetch failed for {ticker} on attempt {attempt+1}: {e}")
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay * (2 ** attempt))

    # If we get here, all retries failed
    logger.error(f"All Yahoo API fetch attempts failed for {ticker}")
    raise Exception(f"Failed to fetch price for {ticker} after {max_retries} attempts")

async def fetch_price_async_http(ticker):
    """Fetch price using aiohttp with retries"""
    import random
    import time

    max_retries = 3
    retry_delay = 2  # seconds

    # List of realistic user agents to rotate
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:108.0) Gecko/20100101 Firefox/108.0',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/108.0.1462.76',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/********'
    ]

    # Use a random user agent for each request
    headers = {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://www.google.com/search?q=' + ticker + '+stock',
        'Origin': 'https://www.google.com',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'cross-site',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0'
    }

    for attempt in range(max_retries):
        try:
            # Add a small random delay to avoid being detected as a bot
            await asyncio.sleep(random.uniform(0.5, 1.5))

            # Use a new random user agent for each attempt
            headers['User-Agent'] = random.choice(user_agents)

            # Try different Yahoo Finance endpoints
            urls = [
                f"https://query1.finance.yahoo.com/v8/finance/chart/{ticker}",
                f"https://query2.finance.yahoo.com/v8/finance/chart/{ticker}",
                f"https://query1.finance.yahoo.com/v7/finance/quote?symbols={ticker}",
                f"https://finance.yahoo.com/quote/{ticker}/history?period1={int(time.time())-86400}&period2={int(time.time())}&interval=1d&filter=history&frequency=1d"
            ]

            # Shuffle the URLs to make the pattern less predictable
            random.shuffle(urls)

            async with aiohttp.ClientSession() as session:
                for url in urls:
                    try:
                        async with session.get(url, headers=headers, timeout=10) as response:
                            if response.status == 200:
                                data = await response.json()

                                # Handle v8 chart API
                                if 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                                    result = data['chart']['result'][0]
                                    if 'meta' in result and 'regularMarketPrice' in result['meta']:
                                        return result['meta']['regularMarketPrice']

                                # Handle v7 quote API
                                if 'quoteResponse' in data and 'result' in data['quoteResponse'] and data['quoteResponse']['result']:
                                    result = data['quoteResponse']['result'][0]
                                    if 'regularMarketPrice' in result:
                                        return result['regularMarketPrice']
                            else:
                                logger.warning(f"HTTP {response.status} when fetching {ticker} from {url}")
                    except Exception as url_error:
                        logger.warning(f"Error with URL {url} for {ticker}: {url_error}")
                        continue

            # If we get here, all URLs failed
            logger.warning(f"All Yahoo Finance URLs failed for {ticker}, attempt {attempt+1}/{max_retries}")

            if attempt < max_retries - 1:
                # Wait before retrying with exponential backoff
                await asyncio.sleep(retry_delay * (2 ** attempt))

        except Exception as e:
            logger.error(f"Async HTTP fetch failed for {ticker} on attempt {attempt+1}: {e}")
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay * (2 ** attempt))

    # If we get here, all retries failed
    logger.error(f"All async HTTP fetch attempts failed for {ticker}")
    raise Exception(f"Failed to fetch price for {ticker} after {max_retries} attempts")

async def fetch_price_alphavantage(ticker):
    """Fetch price using Alpha Vantage API"""
    try:
        # Note: This is a free API key with limited requests
        # For production use, get your own API key from https://www.alphavantage.co/
        api_key = "demo"  # Use demo key for testing
        url = f"https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol={ticker}&apikey={api_key}"
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}

        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()

                    if 'Global Quote' in data and '05. price' in data['Global Quote']:
                        price_str = data['Global Quote']['05. price']
                        return float(price_str)
                else:
                    logger.warning(f"HTTP {response.status} when fetching {ticker} from Alpha Vantage")

        return None
    except Exception as e:
        logger.error(f"Alpha Vantage fetch failed for {ticker}: {e}")
        raise

async def fetch_price_finnhub(ticker):
    """Fetch price using Finnhub API"""
    try:
        # Note: This is a free API key with limited requests
        # For production use, get your own API key from https://finnhub.io/
        api_key = "demo"  # Use demo key for testing
        url = f"https://finnhub.io/api/v1/quote?symbol={ticker}&token={api_key}"
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}

        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()

                    if 'c' in data and data['c'] > 0:
                        return data['c']  # Current price
                else:
                    logger.warning(f"HTTP {response.status} when fetching {ticker} from Finnhub")

        return None
    except Exception as e:
        logger.error(f"Finnhub fetch failed for {ticker}: {e}")
        raise

# Price alert functions
async def check_price_alerts_task():
    """Background task to check price alerts periodically - DISABLED"""
    await bot.wait_until_ready()
    logger.info("Price alert background task is DISABLED to prevent continuous price fetching")

    # This task is now disabled to prevent continuous price fetching
    # If you want to enable it, uncomment the code in the on_ready function

    # The original implementation is kept for reference but not executed
    """
    while not bot.is_closed():
        try:
            await check_all_watchlist_alerts()
            # Check every 15 minutes
            await asyncio.sleep(15 * 60)
        except Exception as e:
            logger.error(f"Error in price alert task: {e}")
            # If there's an error, wait a bit before retrying
            await asyncio.sleep(60)
    """

    # Just return immediately without doing anything
    return

async def check_all_watchlist_alerts():
    """Check all watchlists for price alerts"""
    logger.info("Checking all watchlists for price alerts")

    # Load all user data
    data = load_data()

    # Track which tickers we've already checked to avoid duplicate API calls
    checked_tickers = {}

    # Track alerts that have been triggered to avoid duplicate notifications
    triggered_alerts = []

    for user_id, user_data in data.items():
        if "watchlist" not in user_data:
            continue

        watchlist = user_data["watchlist"]

        for ticker, ticker_data in watchlist.items():
            # Skip if ticker has already been checked
            if ticker in checked_tickers:
                current_price = checked_tickers[ticker]
            else:
                # Fetch current price
                current_price = await fetch_price(ticker)
                checked_tickers[ticker] = current_price

            if current_price is None:
                continue

            # Get entry and target prices
            entry_price = ticker_data.get("entry_price", None)
            target_price = ticker_data.get("target_price", None)

            # Skip if no price targets are set
            if entry_price is None and target_price is None:
                continue

            # Check if price has hit any of the target zones
            alerts = []

            # Check BUY alert (when price hits entry point)
            if entry_price is not None and current_price <= entry_price:
                alerts.append(("BUY", entry_price))

            # Check SELL alert (when price hits target point)
            if target_price is not None and current_price >= target_price:
                alerts.append(("SELL", target_price))

            # Process all triggered alerts
            for alert_type, target_price in alerts:
                # Create a unique identifier for this alert
                alert_id = f"{ticker}_{alert_type}_{datetime.datetime.now().strftime('%Y%m%d')}"

                # Skip if this alert has already been triggered today
                if alert_id in triggered_alerts:
                    continue

                # Add to triggered alerts
                triggered_alerts.append(alert_id)

                # Send alert notification
                await send_price_alert(ticker, current_price, alert_type, entry_price, target_price, user_id)

    logger.info(f"Completed price alert check. Checked {len(checked_tickers)} tickers, triggered {len(triggered_alerts)} alerts.")

async def send_price_alert(ticker, current_price, alert_type, entry_price, target_price, user_id):
    """Send a price alert notification to the main channel"""
    if not portfolio_channel:
        logger.warning("Cannot send price alert: portfolio channel not found")
        return

    try:
        # Get user mention
        user = await bot.fetch_user(int(user_id))
        user_mention = user.mention if user else f"User {user_id}"

        # Load data to get ticker status and risk profile
        data = load_data()
        ticker_data = None
        if str(user_id) in data and "watchlist" in data[str(user_id)] and ticker in data[str(user_id)]["watchlist"]:
            ticker_data = data[str(user_id)]["watchlist"][ticker]

        # Get status and risk profile
        status = "NEUTRAL"
        risk_profile = "C"
        if ticker_data:
            status_mapping = {
                "buy": "BUY",
                "neutral_bullish": "NEUTRAL BUY",
                "neutral_bearish": "NEUTRAL SELL",
                "sell": "SELL"
            }
            internal_status = ticker_data.get("status", "neutral_bullish")
            status = status_mapping.get(internal_status, "NEUTRAL BUY")
            risk_profile = ticker_data.get("risk_profile", "C")

        # Create alert message based on alert type
        if alert_type == "BUY":
            emoji = "🟢"
            message = (
                f"{emoji} **ENTRY PRICE ALERT: {ticker} @ ${current_price:.2f}** {emoji}\n\n"
                f"**{ticker}** has hit the entry price target!\n"
                f"Current price: **${current_price:.2f}**\n"
                f"Entry price: **${entry_price:.2f}**\n"
                f"Target price: **${target_price:.2f}**\n\n"
                f"Status: **{status}** (Setup: **{risk_profile}**)\n"
                f"Please analyze {ticker} again to update your strategy.\n\n"
                f"Alert set by: {user_mention}"
            )
        elif alert_type == "SELL":
            emoji = "🔴"
            message = (
                f"{emoji} **TARGET PRICE ALERT: {ticker} @ ${current_price:.2f}** {emoji}\n\n"
                f"**{ticker}** has hit the target price!\n"
                f"Current price: **${current_price:.2f}**\n"
                f"Entry price: **${entry_price:.2f}**\n"
                f"Target price: **${target_price:.2f}**\n\n"
                f"Status: **{status}** (Setup: **{risk_profile}**)\n"
                f"Please analyze {ticker} again to update your strategy.\n\n"
                f"Alert set by: {user_mention}"
            )
        else:  # Default case
            emoji = "🔔"
            message = (
                f"{emoji} **PRICE ALERT: {ticker} @ ${current_price:.2f}** {emoji}\n\n"
                f"**{ticker}** has hit a price target!\n"
                f"Current price: **${current_price:.2f}**\n"
                f"Entry price: **${entry_price:.2f}**\n"
                f"Target price: **${target_price:.2f}**\n\n"
                f"Status: **{status}** (Setup: **{risk_profile}**)\n"
                f"Please analyze {ticker} again to update your strategy.\n\n"
                f"Alert set by: {user_mention}"
            )

        # Send the alert
        await portfolio_channel.send(message)
        logger.info(f"Sent {alert_type} alert for {ticker} at ${current_price:.2f}")

    except Exception as e:
        logger.error(f"Error sending price alert for {ticker}: {e}")

# Image processing functions
async def download_image(url):
    """Download an image from a URL and convert to base64"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    image_data = await response.read()
                    base64_image = f"data:image/png;base64,{base64.b64encode(image_data).decode('utf-8')}"
                    return image_data, base64_image
                else:
                    logger.error(f"Failed to download image: {response.status}")
                    return None, None
    except Exception as e:
        logger.error(f"Error downloading image: {e}")
        return None, None

# Bot event handlers
@bot.event
async def on_ready():
    """Called when the bot is ready"""
    logger.info(f"{bot.user} has connected to Discord!")
    logger.info(f"Bot is in {len(bot.guilds)} guilds")

    for guild in bot.guilds:
        logger.info(f"Connected to guild: {guild.name} (ID: {guild.id})")

    # Find the portfolio channel
    global portfolio_channel

    # Disable the price alert background task to prevent continuous price fetching
    # bot.loop.create_task(check_price_alerts_task())
    logger.info("Price alert background task is disabled to prevent continuous price fetching")

    # Try to get the guild from the GUILD_ID in .env
    guild = None
    if GUILD_ID:
        guild = bot.get_guild(GUILD_ID)
        logger.info(f"Using configured guild ID: {GUILD_ID}")

    # If we can't find the guild with the ID, try to use the first guild the bot is in
    if not guild and bot.guilds:
        guild = bot.guilds[0]
        logger.info(f"Using first available guild: {guild.name} (ID: {guild.id})")

    if guild:
        try:
            # Check if the bot has the necessary permissions
            bot_member = guild.get_member(bot.user.id)
            if bot_member and bot_member.guild_permissions.manage_channels:
                logger.info(f"Bot has manage_channels permission in {guild.name}")
                # Find or create the bot category
                category = discord.utils.get(guild.categories, name=BOT_CATEGORY_NAME)
                if not category:
                    try:
                        category = await guild.create_category(BOT_CATEGORY_NAME)
                        logger.info(f"Created category: {BOT_CATEGORY_NAME}")
                    except discord.errors.Forbidden:
                        logger.warning(f"Missing permissions to create category: {BOT_CATEGORY_NAME}")
                        # Try to use an existing category or None
                        category = discord.utils.get(guild.categories, name="General") or None

                # Find or create the portfolio channel
                portfolio_channel = discord.utils.get(guild.text_channels, name=PORTFOLIO_CHANNEL_NAME)
                if not portfolio_channel:
                    try:
                        portfolio_channel = await guild.create_text_channel(PORTFOLIO_CHANNEL_NAME, category=category)
                        logger.info(f"Created channel: {PORTFOLIO_CHANNEL_NAME}")
                    except discord.errors.Forbidden:
                        logger.warning(f"Missing permissions to create channel: {PORTFOLIO_CHANNEL_NAME}")
                        # Try to find an existing channel to use for notifications
                        portfolio_channel = discord.utils.get(guild.text_channels, name="general") or None
            else:
                logger.warning(f"Bot doesn't have manage_channels permission in {guild.name}. Using existing channels only.")
                # Try to find existing channels
                portfolio_channel = discord.utils.get(guild.text_channels, name=PORTFOLIO_CHANNEL_NAME)
                if not portfolio_channel:
                    # Try to find a general channel as fallback
                    portfolio_channel = discord.utils.get(guild.text_channels, name="general") or None
        except Exception as e:
            logger.error(f"Error setting up channels: {e}")

    # Register slash commands
    try:
        # First, log all commands that will be registered
        all_commands = [cmd.name for cmd in bot.tree.get_commands()]
        logger.info(f"Attempting to register commands: {', '.join(all_commands)}")

        # Try to sync globally first
        try:
            synced = await bot.tree.sync()
            logger.info(f"Synced {len(synced)} command(s) globally")
            for cmd in synced:
                logger.info(f"Registered command: {cmd.name}")
        except Exception as global_e:
            logger.error(f"Failed to sync commands globally: {global_e}")

            # If global sync fails, try to sync to specific guilds
            if guild:
                try:
                    guild_synced = await bot.tree.sync(guild=guild)
                    logger.info(f"Synced {len(guild_synced)} command(s) to guild {guild.name}")
                    for cmd in guild_synced:
                        logger.info(f"Registered command to guild {guild.id}: {cmd.name}")
                except Exception as guild_e:
                    logger.error(f"Failed to sync commands to guild {guild.id}: {guild_e}")
    except Exception as e:
        logger.error(f"Failed to sync commands: {e}")

    logger.info("Bot is now ready to use!")

@bot.event
async def on_message(message):
    """Called when a message is sent in a channel the bot can see"""
    # Ignore messages from the bot itself
    if message.author == bot.user:
        return

    # Process commands first - this is critical for prefix commands to work
    await bot.process_commands(message)

    # Log command attempts for debugging
    if message.content.startswith(bot.command_prefix):
        command_name = message.content.split()[0][len(bot.command_prefix):]
        logger.info(f"Command attempt: {command_name} by {message.author}")

        # Check if this is a valid command
        command = bot.get_command(command_name)
        if command:
            logger.info(f"Valid command: {command_name}")
        else:
            logger.info(f"Unknown command: {command_name}")

    # Check if the message contains an image attachment
    if message.attachments:
        for attachment in message.attachments:
            # Check if the attachment is an image
            if any(attachment.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.webp']):
                # Check if it's likely a chart image
                is_chart = False
                chart_keywords = ['chart', 'graph', 'candlestick', 'stock', 'trading', 'market', 'price', 'trend',
                                 'volume', 'ichimoku', 'bb', 'macd', 'rsi', 'ema', 'sma', 'fibonacci', 'support',
                                 'resistance', 'bull', 'bear', 'technical', 'analysis', 'indicator', 'analyze', 'analyse']

                # Check message content for chart keywords and explicit analysis requests
                analyze_keywords = ['analyze', 'analyse', 'chart', 'check this', 'look at this', 'what do you think']
                if message.content:
                    # Check for explicit analysis requests
                    if any(keyword in message.content.lower() for keyword in analyze_keywords):
                        logger.info(f"Explicit analysis request found in message content")
                        is_chart = True

                    # Log matching keywords for debugging
                    matching_keywords = [keyword for keyword in chart_keywords if keyword in message.content.lower()]
                    if matching_keywords:
                        logger.info(f"Chart keywords found in message content: {matching_keywords}")

                # Analyze only images that are explicitly identified as charts
                if is_chart:
                    try:
                        logger.info(f"Analyzing chart image: {attachment.filename}")

                        # Show typing indicator
                        async with message.channel.typing():
                            # Try to download the attachment directly
                            image_data = None
                            base64_image = None

                            try:
                                # Download the attachment
                                image_data = await attachment.read()
                                base64_image = f"data:image/png;base64,{base64.b64encode(image_data).decode('utf-8')}"
                                logger.info(f"Successfully downloaded image: {attachment.filename}")
                            except Exception as e:
                                logger.error(f"Error downloading image: {e}")

                            if base64_image:
                                # Analyze the chart image
                                result = await analyze_chart_image(image_data, base64_image)

                                if result:
                                    # Send the analysis (which includes the TICKER-STATUS-RISK format)
                                    await message.channel.send(result["formatted_analysis"])

                                    # Only update existing watchlist entries, don't automatically add new ones
                                    if result["ticker"] != "UNKNOWN":
                                        # Check if ticker is already in user's watchlist
                                        data = load_data()
                                        user_id_str = str(message.author.id)

                                        # Log the current state for debugging
                                        if user_id_str in data and "watchlist" in data[user_id_str]:
                                            if result["ticker"] in data[user_id_str]["watchlist"]:
                                                logger.info(f"Found {result['ticker']} in user's watchlist with status: {data[user_id_str]['watchlist'][result['ticker']]['status']}")
                                            else:
                                                logger.info(f"{result['ticker']} not found in user's watchlist")

                                        if (user_id_str in data and
                                            "watchlist" in data[user_id_str] and
                                            result["ticker"] in data[user_id_str]["watchlist"]):

                                            # Extract entry and target prices
                                            entry_price = None
                                            target_price = None

                                            if result["entry_price"] and result["target_price"]:
                                                try:
                                                    entry_price = float(result["entry_price"])
                                                    target_price = float(result["target_price"])
                                                    logger.info(f"Extracted entry price: ${entry_price}, target price: ${target_price}")
                                                except Exception as e:
                                                    logger.error(f"Error parsing prices: {e}")

                                            # Update existing watchlist entry
                                            old_status = data[user_id_str]["watchlist"][result["ticker"]]["status"]
                                            old_risk = data[user_id_str]["watchlist"][result["ticker"]].get("risk_profile", "C")

                                            # Update status
                                            data[user_id_str]["watchlist"][result["ticker"]]["status"] = result["status"]
                                            logger.info(f"Updated status for {result['ticker']} from {old_status} to {result['status']}")

                                            # Update risk profile
                                            if result["risk_class"]:
                                                data[user_id_str]["watchlist"][result["ticker"]]["risk_profile"] = result["risk_class"]
                                                logger.info(f"Updated risk profile for {result['ticker']} from {old_risk} to {result['risk_class']}")

                                            # Update entry and target prices
                                            if entry_price is not None and target_price is not None:
                                                data[user_id_str]["watchlist"][result["ticker"]]["entry_price"] = entry_price
                                                data[user_id_str]["watchlist"][result["ticker"]]["target_price"] = target_price
                                                logger.info(f"Updated price targets for {result['ticker']}: Entry=${entry_price}, Target=${target_price}")

                                            # Store the analysis text for future reference
                                            if "original_analysis" in result:
                                                data[user_id_str]["watchlist"][result["ticker"]]["analysis"] = result["original_analysis"]
                                                data[user_id_str]["watchlist"][result["ticker"]]["original_analysis"] = result["original_analysis"]
                                                logger.info(f"Stored original analysis text for {result['ticker']}")
                                            elif "raw_analysis" in result:
                                                data[user_id_str]["watchlist"][result["ticker"]]["analysis"] = result["raw_analysis"]
                                                data[user_id_str]["watchlist"][result["ticker"]]["original_analysis"] = result["raw_analysis"]
                                                logger.info(f"Stored raw analysis text for {result['ticker']}")

                                            # Make sure entry/target prices are stored even if they weren't extracted earlier
                                            try:
                                                entry_price = None
                                                target_price = None

                                                # Try to extract from result
                                                if result.get("entry_price") is not None:
                                                    # Try to convert to float if it's a string
                                                    if isinstance(result["entry_price"], str):
                                                        try:
                                                            # Handle German format (comma as decimal separator)
                                                            entry_price = float(result["entry_price"].replace(',', '.'))
                                                        except:
                                                            # Try to extract the number from the string
                                                            match = re.search(r'(\d+[.,]?\d*)', result["entry_price"])
                                                            if match:
                                                                entry_price = float(match.group(1).replace(',', '.'))
                                                    else:
                                                        entry_price = result["entry_price"]

                                                if result.get("target_price") is not None:
                                                    # Try to convert to float if it's a string
                                                    if isinstance(result["target_price"], str):
                                                        try:
                                                            # Handle German format (comma as decimal separator)
                                                            target_price = float(result["target_price"].replace(',', '.'))
                                                        except:
                                                            # Try to extract the number from the string
                                                            match = re.search(r'(\d+[.,]?\d*)', result["target_price"])
                                                            if match:
                                                                target_price = float(match.group(1).replace(',', '.'))
                                                    else:
                                                        target_price = result["target_price"]

                                                # If we have both entry and target prices, update the watchlist
                                                if entry_price is not None and target_price is not None:
                                                    # Update entry and target prices
                                                    data[user_id_str]["watchlist"][result["ticker"]]["entry_price"] = entry_price
                                                    data[user_id_str]["watchlist"][result["ticker"]]["target_price"] = target_price

                                                    # Update last updated timestamp
                                                    data[user_id_str]["watchlist"][result["ticker"]]["last_updated"] = datetime.datetime.now().isoformat()

                                                    logger.info(f"Stored entry/target prices directly from result: ${entry_price}, ${target_price}")
                                                else:
                                                    logger.warning(f"Could not extract valid entry/target prices from result: entry={result.get('entry_price')}, target={result.get('target_price')}")
                                            except Exception as e:
                                                logger.error(f"Error storing entry/target prices: {e}")

                                            # Store the last update timestamp
                                            data[user_id_str]["watchlist"][result["ticker"]]["last_updated"] = datetime.datetime.now().isoformat()

                                            # Save the updated data
                                            save_data(data)
                                            logger.info(f"Saved updated watchlist data for {result['ticker']}")

                                            # Format status for display
                                            status_display = {
                                                "buy": "🟢 BUY",
                                                "neutral_bullish": "🟡 NEUTRAL BULLISH",
                                                "neutral_bearish": "🟠 NEUTRAL BEARISH",
                                                "sell": "🔴 SELL"
                                            }.get(result["status"], result["status"])

                                            # Notify the user
                                            await message.channel.send(
                                                f"Updated {result['ticker']} in your watchlist with status: {status_display} (Setup: {result['risk_class']})"
                                            )

                                            # Check if this is a high-quality setup (A rating) and notify in main channel
                                            if result["risk_class"] == "A" and portfolio_channel and portfolio_channel != message.channel:
                                                # Get status text
                                                status_text = {
                                                    "buy": "BUY",
                                                    "neutral_bullish": "NEUTRAL BUY",
                                                    "neutral_bearish": "NEUTRAL SELL",
                                                    "sell": "SELL"
                                                }.get(result["status"], "NEUTRAL")

                                                # Create notification message
                                                if result["status"] in ["buy", "neutral_bullish"]:
                                                    emoji = "🟢"
                                                    action = "BUY"
                                                    price = entry_price if entry_price is not None else "N/A"
                                                    notification = (
                                                        f"{emoji} **HIGH QUALITY SETUP: {result['ticker']} - {status_text} - A** {emoji}\n\n"
                                                        f"A high-quality BUY setup has been identified for **{result['ticker']}**!\n"
                                                        f"Recommended action: **{action} @ ${price}**\n"
                                                        f"Target price: **${target_price if target_price is not None else 'N/A'}**\n\n"
                                                        f"Check the analysis for more details."
                                                    )
                                                else:
                                                    emoji = "🔴"
                                                    action = "SELL"
                                                    price = entry_price if entry_price is not None else "N/A"
                                                    notification = (
                                                        f"{emoji} **HIGH QUALITY SETUP: {result['ticker']} - {status_text} - A** {emoji}\n\n"
                                                        f"A high-quality SELL setup has been identified for **{result['ticker']}**!\n"
                                                        f"Recommended action: **{action} @ ${price}**\n"
                                                        f"Target price: **${target_price if target_price is not None else 'N/A'}**\n\n"
                                                        f"Check the analysis for more details."
                                                    )

                                                # Send notification to main channel
                                                await portfolio_channel.send(notification)
                                                logger.info(f"Sent high-quality setup notification for {result['ticker']} to main channel")
                                        else:
                                            # Ask if user wants to add to watchlist
                                            await message.channel.send(
                                                f"Would you like to add {result['ticker']} to your watchlist? Use `/addwish {result['ticker']}` to add it."
                                            )
                                else:
                                    await message.channel.send("Sorry, I couldn't analyze this chart image.")
                            else:
                                await message.channel.send("Sorry, I couldn't process this image.")
                    except Exception as e:
                        logger.error(f"Error analyzing chart image: {e}")
                        await message.channel.send("Sorry, an error occurred while analyzing the chart.")

    # Check if the message starts with "BOT" or is from Alpha bot
    if message.content.upper().startswith("BOT") or (message.author.bot and "alpha" in message.author.name.lower()):
        if message.content.upper().startswith("BOT"):
            logger.info(f"Bot request from {message.author}: {message.content}")
            # Extract the content after "BOT"
            content = message.content[3:].strip()
        else:
            logger.info(f"Detected message from Alpha bot: {message.content[:50]}...")
            content = message.content

        try:
            if content:
                # Check if the message contains an image URL
                url_pattern = r'https?://\S+\.(jpg|jpeg|png|gif|webp)(?:\?\S*)?|https?://(?:cdn\.|media\.|images-ext-\d+\.)?discord(?:app)?\.(?:com|net)/\S+'
                urls = re.findall(url_pattern, content, re.IGNORECASE)

                if urls:
                    # Extract the first URL
                    url_match = re.search(url_pattern, content, re.IGNORECASE)
                    if url_match:
                        image_url = url_match.group(0)

                        # Show typing indicator
                        async with message.channel.typing():
                            # Download the image
                            image_data, base64_image = await download_image(image_url)

                            if base64_image:
                                # Analyze the chart image
                                result = await analyze_chart_image(image_data, base64_image)

                                if result:
                                    # Send the analysis
                                    await message.channel.send(result["formatted_analysis"])
                            else:
                                await message.channel.send("Sorry, I couldn't download the image from the URL.")
                else:
                    # Process as a text command or Alpha bot analysis
                    if message.author.bot and "alpha" in message.author.name.lower():
                        # Process Alpha bot message
                        await process_bot_message(content)
                    else:
                        # Process regular BOT command
                        await process_bot_message(content)
        except Exception as e:
            logger.error(f"Error processing BOT/Alpha command: {e}")
            await message.channel.send("Sorry, an error occurred while processing your request.")

# Portfolio management functions
def add_trade(user_id, ticker, shares, price, trade_type):
    """Add a trade to a user's portfolio"""
    data = load_data()

    # Initialize user data if not exists
    if str(user_id) not in data:
        data[str(user_id)] = {
            "trades": [],
            "holdings": {},
            "avg_price": {},
            "watchlist": {}
        }

    user_data = data[str(user_id)]

    # Format ticker
    ticker = ticker.upper().strip()

    # Add trade to history
    trade = {
        "ticker": ticker,
        "shares": shares,
        "price": price,
        "type": trade_type,
        "timestamp": datetime.datetime.now().isoformat()
    }
    user_data["trades"].append(trade)

    # Update holdings and average price
    if trade_type == "buy":
        # Update holdings
        if ticker not in user_data["holdings"]:
            user_data["holdings"][ticker] = 0
            user_data["avg_price"][ticker] = 0

        # Calculate new average price
        current_shares = user_data["holdings"][ticker]
        current_avg_price = user_data["avg_price"][ticker]

        total_shares = current_shares + shares
        total_cost = (current_shares * current_avg_price) + (shares * price)

        if total_shares > 0:
            new_avg_price = total_cost / total_shares
        else:
            new_avg_price = 0

        # Update holdings and average price
        user_data["holdings"][ticker] = total_shares
        user_data["avg_price"][ticker] = new_avg_price

    elif trade_type == "sell":
        # Update holdings
        if ticker in user_data["holdings"]:
            user_data["holdings"][ticker] -= shares

            # If holdings become negative or zero, reset average price
            if user_data["holdings"][ticker] <= 0:
                user_data["holdings"][ticker] = 0
                user_data["avg_price"][ticker] = 0

    # Save updated data
    save_data(data)
    return True

async def get_leaderboard():
    """Get leaderboard of all users sorted by portfolio performance"""
    data = load_data()
    leaderboard = []

    for user_id in data:
        portfolio = await calculate_portfolio(user_id)
        if portfolio:
            # Calculate total performance (open + closed trades)
            total_value = 0
            total_pl = 0
            performance_pct = 0

            # Add open positions value and P/L if available
            if portfolio["total_open_value"] is not None and portfolio["total_open_pl"] is not None:
                total_value += portfolio["total_open_value"]
                total_pl += portfolio["total_open_pl"]

            # Add closed trades P/L
            total_pl += portfolio["total_closed_pl"]

            # Calculate total investment (current cost + realized P/L)
            total_investment = portfolio["total_open_cost"] + portfolio["total_closed_pl"]

            # Calculate performance percentage
            if total_investment > 0:
                performance_pct = (total_pl / total_investment) * 100

            leaderboard.append({
                "user_id": user_id,
                "total_value": total_value,
                "total_pl": total_pl,
                "performance_pct": performance_pct
            })

    # Sort by performance percentage
    if leaderboard:
        leaderboard.sort(key=lambda x: x["performance_pct"], reverse=True)

    return leaderboard

async def calculate_portfolio(user_id):
    """Calculate portfolio value and P/L for a user"""
    data = load_data()

    if str(user_id) not in data:
        return None

    user_data = data[str(user_id)]

    # Open positions (current holdings)
    open_positions = []
    total_open_value = 0
    total_open_cost = 0
    has_price_errors = False

    # Process open positions (current holdings)
    for ticker, shares in user_data["holdings"].items():
        if shares <= 0:
            continue

        avg_price = user_data["avg_price"][ticker]
        current_price = await fetch_price(ticker)

        # Add position to portfolio even if price is unavailable
        position = {
            "ticker": ticker,
            "shares": shares,
            "avg_price": avg_price,
            "current_price": current_price
        }

        # Calculate P/L if price is available
        if current_price is not None:
            position_value = shares * current_price
            position_cost = shares * avg_price
            position_pl = position_value - position_cost
            position_pl_pct = (position_pl / position_cost) * 100 if position_cost > 0 else 0

            position["value"] = position_value
            position["pl"] = position_pl
            position["pl_pct"] = position_pl_pct

            total_open_value += position_value
            total_open_cost += position_cost
        else:
            position["value"] = None
            position["pl"] = None
            position["pl_pct"] = None
            has_price_errors = True

        open_positions.append(position)

    # Calculate closed trades (completed buy and sell pairs)
    closed_trades = []
    total_closed_pl = 0

    # Sort trades by timestamp (oldest first)
    sorted_trades = sorted(user_data["trades"], key=lambda x: x["timestamp"])

    # Process trades by ticker
    ticker_trades = {}
    for trade in sorted_trades:
        ticker = trade["ticker"]
        if ticker not in ticker_trades:
            ticker_trades[ticker] = []
        ticker_trades[ticker].append(trade)

    # Calculate P/L for each ticker
    for ticker, trades in ticker_trades.items():
        # FIFO queue for buy trades
        buy_queue = []

        for trade in trades:
            if trade["type"] == "buy":
                # Add buy trade to queue
                buy_queue.append((trade["shares"], trade["price"]))
            elif trade["type"] == "sell" and buy_queue:
                # Process sell trade against buy queue
                sell_shares = trade["shares"]
                sell_price = trade["price"]

                while sell_shares > 0 and buy_queue:
                    buy_shares, buy_price = buy_queue[0]

                    # Calculate how many shares to match
                    matched_shares = min(sell_shares, buy_shares)

                    # Calculate profit/loss
                    trade_pl = (sell_price - buy_price) * matched_shares
                    trade_pl_pct = ((sell_price - buy_price) / buy_price) * 100 if buy_price > 0 else 0

                    # Add to closed trades
                    closed_trades.append({
                        "ticker": ticker,
                        "shares": matched_shares,
                        "buy_price": buy_price,
                        "sell_price": sell_price,
                        "pl": trade_pl,
                        "pl_pct": trade_pl_pct,
                        "timestamp": trade["timestamp"]
                    })

                    total_closed_pl += trade_pl

                    # Update remaining shares
                    sell_shares -= matched_shares
                    buy_queue[0] = (buy_shares - matched_shares, buy_price)

                    # Remove buy if fully matched
                    if buy_queue[0][0] <= 0:
                        buy_queue.pop(0)

    # Sort closed trades by timestamp (most recent first)
    closed_trades.sort(key=lambda x: x["timestamp"], reverse=True)

    # Prepare result
    result = {
        "open_positions": open_positions,
        "total_open_cost": total_open_cost,
        "closed_trades": closed_trades,
        "total_closed_pl": total_closed_pl,
        "closed_trades_count": len(closed_trades)
    }

    # Only include total value and P/L if all prices were available
    if not has_price_errors and open_positions:
        result["total_open_value"] = total_open_value
        result["total_open_pl"] = total_open_value - total_open_cost
        result["total_open_pl_pct"] = (result["total_open_pl"] / total_open_cost) * 100 if total_open_cost > 0 else 0
    else:
        result["total_open_value"] = None
        result["total_open_pl"] = None
        result["total_open_pl_pct"] = None

    return result

def reset_portfolio(user_id):
    """Reset a user's portfolio"""
    data = load_data()

    if str(user_id) in data:
        # Keep the watchlist but reset everything else
        watchlist = data[str(user_id)].get("watchlist", {})
        data[str(user_id)] = {
            "trades": [],
            "holdings": {},
            "avg_price": {},
            "watchlist": watchlist
        }
        save_data(data)
        return True

    return False

def clear_wishlist(user_id):
    """Clear a user's wishlist"""
    data = load_data()

    if str(user_id) in data:
        # Keep the portfolio but clear the wishlist
        if "watchlist" in data[str(user_id)]:
            data[str(user_id)]["watchlist"] = {}
            save_data(data)
            return True

    return False

def clear_portfolio(user_id):
    """Clear a user's portfolio but keep the user data structure"""
    data = load_data()

    if str(user_id) in data:
        # Reset portfolio data but keep user entry
        data[str(user_id)]["trades"] = []
        data[str(user_id)]["holdings"] = {}
        data[str(user_id)]["avg_price"] = {}
        save_data(data)
        return True

    return False

# Slash commands for portfolio management
@bot.tree.command(name="buy", description="Add a buy trade to your portfolio")
async def buy(interaction: discord.Interaction, ticker: str, shares: float, price: float):
    """Add a buy trade to your portfolio"""
    user_id = interaction.user.id

    # Defer the response
    await interaction.response.defer(ephemeral=False)

    if shares <= 0 or price <= 0:
        await interaction.followup.send("Shares and price must be positive numbers.")
        return

    # Format ticker symbol
    ticker = ticker.upper().strip()

    # Add the trade
    success = add_trade(user_id, ticker, shares, price, "buy")

    if success:
        # Try to get current price for P/L calculation
        current_price = await fetch_price(ticker)

        # Send detailed message to the portfolio channel
        if portfolio_channel:
            # Add current price and P/L info if available
            price_info = ""
            if current_price is not None:
                pl = (current_price - price) * shares
                pl_pct = ((current_price - price) / price * 100)
                price_info = f" | Current: ${current_price:.2f} | P/L: ${pl:.2f} ({pl_pct:.2f}%)"

            user = interaction.user
            await portfolio_channel.send(f"🟢 **BUY:** {user.mention} added {shares} shares of {ticker} at ${price:.2f}{price_info}")

        # Send confirmation to the user
        await interaction.followup.send(f"Added {shares} shares of {ticker} at ${price:.2f} to your portfolio.")
    else:
        await interaction.followup.send("Failed to add trade. Please try again.")

@bot.tree.command(name="sell", description="Add a sell trade to your portfolio")
async def sell(interaction: discord.Interaction, ticker: str, shares: float, price: float):
    """Add a sell trade to your portfolio"""
    user_id = interaction.user.id

    # Defer the response
    await interaction.response.defer(ephemeral=False)

    if shares <= 0 or price <= 0:
        await interaction.followup.send("Shares and price must be positive numbers.")
        return

    # Format ticker
    ticker = ticker.upper().strip()

    # Check if user has enough shares to sell
    data = load_data()
    if str(user_id) not in data or ticker not in data[str(user_id)]["holdings"] or data[str(user_id)]["holdings"][ticker] < shares:
        await interaction.followup.send(f"You don't have enough shares of {ticker} to sell.")
        return

    # Get the average purchase price for P/L calculation
    avg_price = data[str(user_id)]["avg_price"][ticker]

    success = add_trade(user_id, ticker, shares, price, "sell")

    if success:
        # Calculate P/L
        pl = (price - avg_price) * shares
        pl_pct = ((price - avg_price) / avg_price * 100) if avg_price > 0 else 0

        # Send detailed message to the portfolio channel
        if portfolio_channel:
            user = interaction.user
            await portfolio_channel.send(
                f"🔴 **SELL:** {user.mention} sold {shares} shares of {ticker} at ${price:.2f}\n"
                f"P/L: ${pl:.2f} ({pl_pct:.2f}%)"
            )

        # Send confirmation to the user
        await interaction.followup.send(
            f"Sold {shares} shares of {ticker} at ${price:.2f}.\n"
            f"P/L: ${pl:.2f} ({pl_pct:.2f}%)"
        )
    else:
        await interaction.followup.send("Failed to add trade. Please try again.")

@bot.tree.command(name="portfolio", description="View your portfolio")
async def portfolio(interaction: discord.Interaction):
    """View your portfolio"""
    user_id = interaction.user.id

    # Defer the response since fetching prices might take time
    await interaction.response.defer(ephemeral=False)

    portfolio_data = await calculate_portfolio(user_id)

    if not portfolio_data or (not portfolio_data["open_positions"] and not portfolio_data["closed_trades"]):
        await interaction.followup.send("You don't have any trades in your portfolio yet.")
        return

    # Format the portfolio message
    message = f"📊 **Portfolio for {interaction.user.display_name}:**\n\n"

    # Open positions section
    if portfolio_data["open_positions"]:
        message += "**Open Positions**\n"

        # Add total value and percentage change
        if portfolio_data["total_open_value"] is not None and portfolio_data["total_open_pl_pct"] is not None:
            total_value_str = f"Total Value: ${portfolio_data['total_open_value']:.2f}"
            pct_change_str = f"Change: {'+' if portfolio_data['total_open_pl_pct'] >= 0 else ''}{portfolio_data['total_open_pl_pct']:.2f}%"
            message += f"{total_value_str} | {pct_change_str}\n"
        else:
            message += "Total Value: Not available\n"

        # Add each position
        for position in portfolio_data["open_positions"]:
            ticker = position["ticker"]
            shares = position["shares"]
            avg_price = position["avg_price"]

            position_str = f"{ticker}: {shares} shares @ ${avg_price:.2f}"

            if position["current_price"] is not None:
                current_price = position["current_price"]
                value = position["value"]
                pl = position["pl"]
                pl_pct = position["pl_pct"]

                position_str += f" | Current: ${current_price:.2f} | Value: ${value:.2f} | P/L: ${pl:.2f} ({'+' if pl_pct >= 0 else ''}{pl_pct:.2f}%)"

            message += f"- {position_str}\n"

        message += "\n"

    # Closed trades section
    if portfolio_data["closed_trades"]:
        message += "**Recent Closed Trades**\n"
        message += f"Total Realized P/L: ${portfolio_data['total_closed_pl']:.2f}\n"

        # Show up to 5 most recent closed trades
        for trade in portfolio_data["closed_trades"][:5]:
            ticker = trade["ticker"]
            shares = trade["shares"]
            buy_price = trade["buy_price"]
            sell_price = trade["sell_price"]
            pl = trade["pl"]
            pl_pct = trade["pl_pct"]

            trade_str = f"{ticker}: {shares} shares | Buy: ${buy_price:.2f} | Sell: ${sell_price:.2f} | P/L: ${pl:.2f} ({'+' if pl_pct >= 0 else ''}{pl_pct:.2f}%)"
            message += f"- {trade_str}\n"

        # If there are more closed trades, add a note
        if len(portfolio_data["closed_trades"]) > 5:
            message += f"...and {len(portfolio_data['closed_trades']) - 5} more closed trades\n"

    # Send the portfolio to the user
    await interaction.followup.send(message)

# Note: Removed redundant reset commands in favor of clearportfolio command

@bot.tree.command(name="clearportfolio", description="Reset your portfolio data while keeping your wishlist")
async def clearportfolio(interaction: discord.Interaction):
    """Reset your portfolio data while keeping your wishlist"""
    user_id = interaction.user.id

    # Defer the response
    await interaction.response.defer(ephemeral=False)

    # Ask for confirmation
    confirm_view = discord.ui.View(timeout=60)

    async def confirm_callback(button_interaction):
        if button_interaction.user.id != user_id:
            await button_interaction.response.send_message("This is not your confirmation dialog.", ephemeral=True)
            return

        success = clear_portfolio(user_id)
        if success:
            # Notify the portfolio channel
            if portfolio_channel:
                await portfolio_channel.send(f"🗑️ **CLEAR PORTFOLIO:** {interaction.user.mention} has cleared their portfolio data.")

            await button_interaction.response.send_message("Your portfolio has been cleared. Your wishlist remains unchanged.")
        else:
            await button_interaction.response.send_message("Error clearing your portfolio. Please try again.")

    async def cancel_callback(button_interaction):
        if button_interaction.user.id != user_id:
            await button_interaction.response.send_message("This is not your confirmation dialog.", ephemeral=True)
            return

        await button_interaction.response.send_message("Portfolio clearing cancelled.")

    # Create buttons
    confirm_button = discord.ui.Button(label="Confirm Clear", style=discord.ButtonStyle.danger)
    confirm_button.callback = confirm_callback

    cancel_button = discord.ui.Button(label="Cancel", style=discord.ButtonStyle.secondary)
    cancel_button.callback = cancel_callback

    # Add buttons to view
    confirm_view.add_item(confirm_button)
    confirm_view.add_item(cancel_button)

    # Send confirmation message
    await interaction.followup.send(
        "⚠️ **WARNING:** This will clear all your portfolio data (trades, holdings, etc.) but keep your wishlist. "
        "This action cannot be undone. Are you sure?",
        view=confirm_view
    )

@bot.tree.command(name="clearwishlist", description="Clear your wishlist")
async def clearwishlist(interaction: discord.Interaction):
    """Clear your wishlist"""
    user_id = interaction.user.id

    # Defer the response
    await interaction.response.defer(ephemeral=False)

    # Ask for confirmation
    confirm_view = discord.ui.View(timeout=60)

    async def confirm_callback(button_interaction):
        if button_interaction.user.id != user_id:
            await button_interaction.response.send_message("This is not your confirmation dialog.", ephemeral=True)
            return

        success = clear_wishlist(user_id)
        if success:
            # Notify the portfolio channel
            if portfolio_channel:
                await portfolio_channel.send(f"👀 **CLEAR WISHLIST:** {interaction.user.mention} has cleared their wishlist.")

            await button_interaction.response.send_message("Your wishlist has been cleared. Your portfolio remains unchanged.")
        else:
            await button_interaction.response.send_message("Error clearing your wishlist. Please try again.")

    async def cancel_callback(button_interaction):
        if button_interaction.user.id != user_id:
            await button_interaction.response.send_message("This is not your confirmation dialog.", ephemeral=True)
            return

        await button_interaction.response.send_message("Wishlist clearing cancelled.")

    # Create buttons
    confirm_button = discord.ui.Button(label="Confirm Clear", style=discord.ButtonStyle.danger)
    confirm_button.callback = confirm_callback

    cancel_button = discord.ui.Button(label="Cancel", style=discord.ButtonStyle.secondary)
    cancel_button.callback = cancel_callback

    # Add buttons to view
    confirm_view.add_item(confirm_button)
    confirm_view.add_item(cancel_button)

    # Send confirmation message
    await interaction.followup.send(
        "⚠️ **WARNING:** This will clear all tickers from your wishlist. "
        "This action cannot be undone. Are you sure?",
        view=confirm_view
    )

@bot.tree.command(name="leaderboard", description="View the portfolio leaderboard")
async def leaderboard(interaction: discord.Interaction):
    """View the portfolio leaderboard"""
    # Defer the response since calculating the leaderboard might take time
    await interaction.response.defer(ephemeral=False)

    leaderboard_data = await get_leaderboard()

    if not leaderboard_data:
        await interaction.followup.send("No portfolios found for the leaderboard.")
        return

    # Format the leaderboard message
    message = "🏆 **LEADERBOARD:**\n\n"

    for i, entry in enumerate(leaderboard_data[:10], 1):  # Show top 10
        try:
            user = await bot.fetch_user(int(entry["user_id"]))
            username = user.display_name if user else f"User {entry['user_id']}"
        except:
            username = f"User {entry['user_id']}"

        # Format performance with + sign for positive values
        perf_sign = "+" if entry['performance_pct'] >= 0 else ""

        # Format total value if available
        value_str = ""
        if entry['total_value'] > 0:
            value_str = f" | ${entry['total_value']:.2f}"

        # Show rank, username, performance percentage, and total value
        message += f"{i}. {username} – {perf_sign}{entry['performance_pct']:.2f}%{value_str}\n"

    # Send the leaderboard
    await interaction.followup.send(message)

# Slash commands for wishlist management
@bot.tree.command(name="wishlist", description="View your wishlist")
async def wishlist(interaction: discord.Interaction):
    """View your wishlist"""
    user_id = interaction.user.id

    # Defer the response since fetching prices might take time
    await interaction.response.defer(ephemeral=False)

    # Get watchlist data
    watchlist_data = get_watchlist(user_id)

    if not watchlist_data:
        await interaction.followup.send("You don't have any tickers in your wishlist yet.")
        return

    # Fetch current prices for all tickers
    watchlist_items = []
    for ticker, data in watchlist_data.items():
        status = data["status"]
        risk_profile = data["risk_profile"]

        # Format status for display
        status_display = {
            "buy": "🟢 BUY",
            "neutral_bullish": "🟡 NEUTRAL BULLISH",
            "neutral_bearish": "🟠 NEUTRAL BEARISH",
            "sell": "🔴 SELL"
        }.get(status, status)

        # Ensure risk profile is valid
        if risk_profile not in ["A", "B", "C"]:
            risk_profile = "C"  # Default to C if invalid

        # Format price targets
        alert_str = "No price targets set"

        # Get entry and target prices
        entry_price = data.get("entry_price")
        target_price = data.get("target_price")

        # Debug log the entry and target prices
        logger.info(f"Watchlist display for {ticker}: entry_price={entry_price}, target_price={target_price}, type(entry_price)={type(entry_price)}, type(target_price)={type(target_price)}")

        # Handle different types of price values
        if entry_price is not None and target_price is not None:
            try:
                # Convert to float if they're strings
                if isinstance(entry_price, str):
                    entry_price = float(entry_price.replace(',', '.'))
                if isinstance(target_price, str):
                    target_price = float(target_price.replace(',', '.'))

                # Format as currency
                alert_str = f"Entry: ${float(entry_price):.2f}, Target: ${float(target_price):.2f}"
                logger.info(f"Formatted price targets for {ticker}: {alert_str}")
            except (ValueError, TypeError) as e:
                # If conversion fails, just display as is
                alert_str = f"Entry: {entry_price}, Target: {target_price}"
                logger.warning(f"Could not format price targets for {ticker}: {e}")

        # Fetch current price - simple approach with no retries
        current_price = await fetch_price(ticker)

        # Format price string
        if current_price is not None:
            price_str = f"${current_price:.2f}"
        else:
            price_str = "Price unavailable"
            logger.error(f"Could not fetch price for {ticker} - check Yahoo Finance access")

        # Add to list
        watchlist_items.append(f"**{ticker}** - {status_display} (Setup: {risk_profile})\n{price_str} | {alert_str}")

        # Log the formatted display for debugging
        logger.info(f"Formatted watchlist item: {ticker} - {status_display} (Setup: {risk_profile})")

    # Create embed
    embed = discord.Embed(
        title="Your Watchlist",
        description="\n\n".join(watchlist_items),
        color=discord.Color.blue()
    )

    embed.set_footer(text="Hedgefund Simulator | Verwende /addwish zum Hinzufügen und /removewish zum Entfernen")

    await interaction.followup.send(embed=embed)

@bot.tree.command(name="addwish", description="Add a ticker to your wishlist")
async def addwish(
    interaction: discord.Interaction,
    ticker: str,
    risk_profile: str = None
):
    """Add a ticker to your wishlist"""
    user_id = interaction.user.id

    # Defer the response
    await interaction.response.defer(ephemeral=False)

    # Format ticker
    ticker = ticker.upper().strip()

    # Try to validate ticker by checking if we can get a price
    current_price = await fetch_price(ticker)
    price_warning = ""
    if current_price is None:
        price_warning = f"\n⚠️ Warning: Could not fetch current price for '{ticker}'. The ticker has been added anyway, but please verify it's correct."
        logger.warning(f"Could not verify ticker '{ticker}' but adding it to wishlist anyway")

    # Validate risk profile
    if risk_profile is not None and risk_profile.upper() not in ["A", "B", "C"]:
        await interaction.followup.send(f"Invalid risk profile: {risk_profile}. Please use A, B, or C.")
        return

    # Format risk profile
    formatted_risk_profile = risk_profile.upper() if risk_profile else "B"  # Default to B (medium risk)

    # Add to watchlist with default status (neutral_bullish) - alerts will be set by GPT analysis
    success = add_to_watchlist(user_id, ticker, "neutral_bullish", None, None, formatted_risk_profile)

    if success:
        # Format risk profile
        risk_str = f" (Setup: {formatted_risk_profile})"

        # Notify the portfolio channel
        if portfolio_channel:
            user = interaction.user
            if current_price is not None:
                await portfolio_channel.send(
                    f"👀 **WISHLIST ADD:** {user.mention} added {ticker} to their wishlist{risk_str}.\n"
                    f"Current price: ${current_price:.2f}{price_warning}\n"
                    f"💡 Price targets will be set automatically when GPT analyzes this stock."
                )
            else:
                await portfolio_channel.send(
                    f"👀 **WISHLIST ADD:** {user.mention} added {ticker} to their wishlist{risk_str}.\n"
                    f"Current price: unavailable{price_warning}\n"
                    f"💡 Price targets will be set automatically when GPT analyzes this stock."
                )

        # Send confirmation to the user
        if current_price is not None:
            await interaction.followup.send(
                f"👀 **WISHLIST ADD:** {ticker} has been added to your wishlist{risk_str}.\n"
                f"Current price: ${current_price:.2f}{price_warning}\n"
                f"💡 Price targets will be set automatically when GPT analyzes this stock."
            )
        else:
            await interaction.followup.send(
                f"👀 **WISHLIST ADD:** {ticker} has been added to your wishlist{risk_str}.\n"
                f"Current price: unavailable{price_warning}\n"
                f"💡 Price targets will be set automatically when GPT analyzes this stock."
            )
    else:
        await interaction.followup.send("Error adding to wishlist. Please try again.")

@bot.tree.command(name="removewish", description="Remove a ticker from your wishlist")
async def removewish(interaction: discord.Interaction, ticker: str):
    """Remove a ticker from your wishlist"""
    user_id = interaction.user.id

    # Format ticker
    ticker = ticker.upper().strip()

    # Remove from watchlist
    success = remove_from_watchlist(user_id, ticker)

    if success:
        # Notify the portfolio channel
        if portfolio_channel:
            user = interaction.user
            await portfolio_channel.send(f"👀 **WISHLIST REMOVE:** {user.mention} removed {ticker} from their wishlist.")

        # Send confirmation to the user
        await interaction.followup.send(f"👀 **WISHLIST REMOVE:** {ticker} has been removed from your wishlist.")
    else:
        await interaction.followup.send(f"{ticker} is not in your wishlist or there was an error removing it.")

# Manual command to sync slash commands
@bot.command(name="sync", hidden=True)
@commands.is_owner()
async def sync_command(ctx):
    """Manually sync slash commands (owner only)"""
    try:
        logger.info(f"Manual sync requested by {ctx.author}")
        await ctx.send("🔄 Starting command synchronization...")

        # Clear existing commands first
        try:
            bot.tree.clear_commands(guild=None)
            logger.info("Cleared global commands")
            if ctx.guild:
                bot.tree.clear_commands(guild=ctx.guild)
                logger.info(f"Cleared guild commands for {ctx.guild.name}")
        except Exception as clear_e:
            logger.warning(f"Error clearing commands: {clear_e}")

        # Wait a moment for clearing to take effect
        await asyncio.sleep(2)

        # Try to sync globally first
        try:
            synced = await bot.tree.sync()
            await ctx.send(f"✅ Synced {len(synced)} command(s) globally")
            logger.info(f"Manually synced {len(synced)} command(s) globally")

            # List all synced commands
            command_list = "\n".join([f"- /{cmd.name}" for cmd in synced])
            await ctx.send(f"Available commands:\n{command_list}")

        except Exception as e:
            logger.error(f"Failed to sync commands globally: {e}")
            await ctx.send(f"❌ Failed to sync commands globally: {e}")

            # Try to sync to the current guild
            try:
                guild_synced = await bot.tree.sync(guild=ctx.guild)
                await ctx.send(f"✅ Synced {len(guild_synced)} command(s) to this guild")
                logger.info(f"Manually synced {len(guild_synced)} command(s) to guild {ctx.guild.id}")

                # List all synced commands
                command_list = "\n".join([f"- /{cmd.name}" for cmd in guild_synced])
                await ctx.send(f"Available commands:\n{command_list}")

            except Exception as guild_e:
                logger.error(f"Failed to sync commands to guild {ctx.guild.id}: {guild_e}")
                await ctx.send(f"❌ Failed to sync commands to this guild: {guild_e}")

        # Additional info
        await ctx.send("ℹ️ **Note:** It may take up to 1 hour for slash commands to appear in Discord. Try restarting your Discord client if they don't appear immediately.")

    except Exception as e:
        logger.error(f"Error in sync command: {e}")
        await ctx.send(f"❌ Error: {e}")

# Force sync command for immediate synchronization
@bot.command(name="forcesync", hidden=True)
@commands.is_owner()
async def force_sync_command(ctx):
    """Force sync slash commands to current guild (owner only)"""
    try:
        logger.info(f"Force sync requested by {ctx.author} for guild {ctx.guild.id}")
        await ctx.send("🔄 Force syncing commands to this guild...")

        # Sync to current guild specifically
        synced = await bot.tree.sync(guild=ctx.guild)
        await ctx.send(f"✅ Force synced {len(synced)} command(s) to {ctx.guild.name}")
        logger.info(f"Force synced {len(synced)} command(s) to guild {ctx.guild.id}")

        # List all synced commands
        command_list = "\n".join([f"- /{cmd.name}" for cmd in synced])
        await ctx.send(f"Available commands:\n{command_list}")

        await ctx.send("ℹ️ Guild-specific commands should appear immediately!")

    except Exception as e:
        logger.error(f"Error in force sync command: {e}")
        await ctx.send(f"❌ Error: {e}")

# Slash command for help
@bot.tree.command(name="help", description="Show all available commands and features")
async def help_command(interaction: discord.Interaction):
    """Show all available commands and features"""
    # Defer the response
    await interaction.response.defer(ephemeral=False)

    # Create an embed for better formatting
    embed = discord.Embed(
        title="📈 Hedgefund Simulator - Hilfe",
        description="Simuliere deine Trading-Strategien ohne echtes Geld! Hier sind alle verfügbaren Befehle:",
        color=0x00ff00
    )

    # Portfolio Simulation Commands
    embed.add_field(
        name="💼 Portfolio Simulation",
        value=(
            "`/buy <ticker> <shares> <price>` - Simuliere einen Kauf\n"
            "`/sell <ticker> <shares> <price>` - Simuliere einen Verkauf\n"
            "`/portfolio` - Zeige dein simuliertes Portfolio\n"
            "`/clearportfolio` - Portfolio zurücksetzen\n"
            "`/leaderboard` - Rangliste der besten Trader"
        ),
        inline=False
    )

    # Watchlist Management Commands
    embed.add_field(
        name="⭐ Watchlist Verwaltung",
        value=(
            "`/wishlist` - Zeige deine Watchlist\n"
            "`/addwish <ticker> [risk]` - Aktie zur Watchlist hinzufügen\n"
            "`/removewish <ticker>` - Aktie von Watchlist entfernen\n"
            "`/clearwishlist` - Gesamte Watchlist löschen"
        ),
        inline=False
    )

    # Chart Analysis Commands
    embed.add_field(
        name="📈 Chart Analyse",
        value=(
            "`bot analyze <image_url>` - Chart-Bild analysieren\n"
            "`bot analysiere <image_url>` - Deutsche Chart-Analyse\n"
            "💡 Verwende `/c <ticker>` für externe Alpha Bot Analysen"
        ),
        inline=False
    )

    embed.set_footer(text="Hedgefund Simulator v1.0 | Alle Trades sind simuliert - kein echtes Geld!")

    await interaction.followup.send(embed=embed)

# Command to list all available slash commands
@bot.command(name="commands")
async def list_commands(ctx):
    """List all available slash commands"""
    try:
        # Get all commands
        all_commands = bot.tree.get_commands()

        if all_commands:
            # Format command list
            command_list = "\n".join([f"- /{cmd.name}: {cmd.description}" for cmd in all_commands])
            await ctx.send(f"**Available Slash Commands:**\n{command_list}\n\nIf commands don't appear in Discord, try using `!sync` (bot owner only) to register them.")
        else:
            await ctx.send("No slash commands are currently registered. Ask the bot owner to use `!sync` to register them.")

    except Exception as e:
        logger.error(f"Error listing commands: {e}")
        await ctx.send(f"❌ Error listing commands: {e}")

# Alpha bot integration and BOT command processing is now handled in the main on_message handler above

# Run the bot
def run_bot():
    """Start the bot"""
    logger.info("Starting UnifiedBot...")
    try:
        bot.run(DISCORD_TOKEN)
    except Exception as e:
        logger.critical(f"Error starting the bot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_bot()
